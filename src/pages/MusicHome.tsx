import React, { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from 'react';
import { 
  <PERSON>, Sparkles, Play, Heart, MessageCircle, Share2,
  Home, Volume2, Pause, SkipForward, Send,
  TrendingUp, Hash, Clock, ChevronRight, Zap, Star,
  Headphones, Gift, Bell, Loader2, X, Flame,
  Mic, Mic<PERSON>ff, ChevronUp, Command, Layers,
  ArrowRight, CircleEllipsis, Brain, Plus,
  User, PenTool, Palette, ChevronDown, Sparkle,
  Trophy, Users, Feather, Calendar, Target,
  BookOpen, Smile, Coffee, Moon, Sun
} from 'lucide-react';

// ==================== 类型定义 ====================
interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  type: 'note' | 'star' | 'heart' | 'wave';
  rotation: number;
  rotationSpeed: number;
  lifespan: number;
  maxLife: number;
}

interface MoodColor {
  primary: string;
  secondary: string;
  particles: string[];
  glow: string;
  background: string;
  accent: string;
}

interface MusicCard {
  id: string;
  title: string;
  artist: string;
  mood: string;
  plays: number;
  likes: number;
  coverGradient: string;
  duration: string;
  isPlaying?: boolean;
  waveform?: number[];
  tags: string[];
  createdAt: Date;
  storyBrief?: string;
  energy: number;
  resonanceCount?: number;
  remixCount?: number;
}

interface StoryCard {
  id: string;
  title: string;
  author: string;
  story: string;
  musicId: string;
  coverGradient: string;
  mood: string;
  resonanceCount: number;
  createdAt: Date;
  duration: string;
}

interface ChainChallenge {
  id: string;
  title: string;
  participants: number;
  segments: number;
  coverGradient: string;
  deadline: Date;
  reward: string;
}

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  isTyping?: boolean;
}

interface AISuggestion {
  id: string;
  text: string;
  icon?: React.ComponentType<any>;
  action: () => void;
}

interface AITalk {
  message: string;
  quickReplies?: { text: string; action: string }[];
  mood?: 'happy' | 'excited' | 'calm' | 'curious';
}

// ==================== 配色系统 ====================
const moodColors: Record<string, MoodColor> = {
  happy: {
    primary: 'from-yellow-400 via-amber-400 to-orange-400',
    secondary: 'from-amber-300 to-yellow-300',
    particles: ['#FCD34D', '#FB923C', '#FBBF24', '#F59E0B'],
    glow: 'shadow-yellow-400/50',
    background: 'from-amber-50 to-orange-50',
    accent: '#F59E0B'
  },
  calm: {
    primary: 'from-blue-400 via-cyan-400 to-teal-400',
    secondary: 'from-teal-300 to-blue-300',
    particles: ['#60A5FA', '#06B6D4', '#14B8A6', '#10B981'],
    glow: 'shadow-cyan-400/50',
    background: 'from-blue-50 to-cyan-50',
    accent: '#06B6D4'
  },
  excited: {
    primary: 'from-purple-400 via-pink-400 to-rose-400',
    secondary: 'from-rose-300 to-purple-300',
    particles: ['#C084FC', '#F472B6', '#EC4899', '#DB2777'],
    glow: 'shadow-pink-400/50',
    background: 'from-purple-50 to-pink-50',
    accent: '#EC4899'
  },
  curious: {
    primary: 'from-indigo-400 via-purple-400 to-violet-400',
    secondary: 'from-violet-300 to-indigo-300',
    particles: ['#818CF8', '#A78BFA', '#C084FC', '#E879F9'],
    glow: 'shadow-purple-400/50',
    background: 'from-indigo-50 to-purple-50',
    accent: '#A78BFA'
  }
};

// ==================== AI语言库 ====================
const aiTalkLibrary: AITalk[] = [
  {
    message: "最近大家都在玩表白神曲，我们也来创作一首吗？",
    quickReplies: [
      { text: "好呀！", action: "create_love_song" },
      { text: "听听别人的", action: "browse_love_songs" }
    ],
    mood: 'excited'
  },
  {
    message: "今天心情怎么样？让我为你推荐合适的音乐～",
    quickReplies: [
      { text: "很开心", action: "mood_happy" },
      { text: "有点累", action: "mood_tired" }
    ],
    mood: 'calm'
  },
  {
    message: "发现了一首宝藏歌曲，90%的人听了都会单曲循环！",
    quickReplies: [
      { text: "快给我听", action: "play_treasure" },
      { text: "收藏起来", action: "save_treasure" }
    ],
    mood: 'happy'
  },
  {
    message: "深夜了，需要一些助眠音乐吗？",
    quickReplies: [
      { text: "来一首", action: "play_sleep" },
      { text: "我还不困", action: "browse_energetic" }
    ],
    mood: 'calm'
  }
];

// ==================== 主组件 ====================
const MusicHomePage: React.FC = () => {
  // ========== 状态管理 ==========
  const [currentMood, setCurrentMood] = useState<string>('excited');
  const [particles, setParticles] = useState<Particle[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [currentPlayingId, setCurrentPlayingId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<'home'>('home');
  
  // AI 相关状态
  const [aiMessages, setAiMessages] = useState<AIMessage[]>([]);
  const [showAiChat, setShowAiChat] = useState(false);
  const [aiInputText, setAiInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentAiTalk, setCurrentAiTalk] = useState<AITalk>(aiTalkLibrary[0]);
  const [aiTalkMessage, setAiTalkMessage] = useState('');
  const [showQuickReply, setShowQuickReply] = useState(false);
  const [aiButtonPulse, setAiButtonPulse] = useState(true);
  const [hasInitialMessage, setHasInitialMessage] = useState(false);
  
  // 长按相关
  const [isLongPressing, setIsLongPressing] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Refs
  const animationRef = useRef<number | undefined>(undefined);
  const aiButtonRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);

  // ========== 模拟数据生成 ==========
  const generateMusicCards = useCallback((): MusicCard[] => {
    const titles = [
      '夏日微风', '深夜独白', '清晨第一缕阳光', '雨后彩虹', '星空下的约定',
      '咖啡馆的下午', '地铁上的思念', '窗外的风景', '回忆的旋律', '未来的憧憬'
    ];
    
    const artists = [
      '小明的心情', '月光诗人', '都市游吟者', '梦想家小李', '音乐精灵'
    ];
    
    const tags = [
      ['治愈', '轻音乐'], ['深夜', 'emo'], ['励志', '早安'], ['浪漫', '爱情'],
      ['怀旧', '青春'], ['解压', '放松'], ['失眠', '助眠'], ['学习', '专注']
    ];
    
    const moods = Object.keys(moodColors);
    
    return Array.from({ length: 10 }, (_, i) => ({
      id: `music-${Date.now()}-${i}`,
      title: titles[i % titles.length],
      artist: artists[i % artists.length],
      mood: moods[i % moods.length],
      plays: Math.floor(Math.random() * 10000) + 100,
      likes: Math.floor(Math.random() * 5000) + 50,
      coverGradient: moodColors[moods[i % moods.length]].primary,
      duration: `${Math.floor(Math.random() * 3) + 2}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`,
      waveform: Array.from({ length: 30 }, () => Math.random() * 100),
      tags: tags[i % tags.length],
      createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      storyBrief: '这是一个关于' + titles[i % titles.length] + '的故事...',
      energy: Math.floor(Math.random() * 100),
      resonanceCount: Math.floor(Math.random() * 1000) + 100,
      remixCount: Math.floor(Math.random() * 50) + 5
    }));
  }, []);

  const generateStoryCards = useCallback((): StoryCard[] => {
    return [
      {
        id: 'story-1',
        title: '那个夏天的告别',
        author: '追光者',
        story: '毕业那天，我们约定十年后再见。如今音乐响起，仿佛又回到了那个充满阳光的教室...',
        musicId: 'music-1',
        coverGradient: moodColors.happy.primary,
        mood: 'happy',
        resonanceCount: 2341,
        createdAt: new Date(),
        duration: '3:24'
      },
      {
        id: 'story-2',
        title: '深夜的地铁站',
        author: '城市夜归人',
        story: '凌晨的最后一班地铁，看着窗外飞驰而过的灯光，突然想起了远方的你...',
        musicId: 'music-2',
        coverGradient: moodColors.calm.primary,
        mood: 'calm',
        resonanceCount: 1892,
        createdAt: new Date(),
        duration: '4:12'
      },
      {
        id: 'story-3',
        title: '妈妈的生日歌',
        author: '小棉袄',
        story: '第一次用AI为妈妈写歌，她听着听着就哭了，说这是最好的生日礼物...',
        musicId: 'music-3',
        coverGradient: moodColors.excited.primary,
        mood: 'excited',
        resonanceCount: 5678,
        createdAt: new Date(),
        duration: '2:58'
      }
    ];
  }, []);

  const generateChainChallenges = useCallback((): ChainChallenge[] => {
    return [
      {
        id: 'chain-1',
        title: '用5个字创作一首歌',
        participants: 1234,
        segments: 8,
        coverGradient: 'from-purple-400 to-pink-400',
        deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        reward: '限定音色包'
      },
      {
        id: 'chain-2',
        title: '接龙：夏日恋爱物语',
        participants: 892,
        segments: 12,
        coverGradient: 'from-orange-400 to-pink-400',
        deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        reward: '7天会员'
      }
    ];
  }, []);

  const [musicCards] = useState<MusicCard[]>(generateMusicCards());
  const [storyCards] = useState<StoryCard[]>(generateStoryCards());
  const [chainChallenges] = useState<ChainChallenge[]>(generateChainChallenges());

  // ========== 生命周期 ==========
  useEffect(() => {
    updateAiTalk();
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    const talkTimer = setInterval(() => {
      updateAiTalk();
    }, 30000);
    
    return () => {
      clearInterval(timer);
      clearInterval(talkTimer);
    };
  }, []);

  // 打字机效果
  useEffect(() => {
    if (currentAiTalk) {
      setAiTalkMessage('');
      let index = 0;
      const text = currentAiTalk.message;
      
      const typeChar = () => {
        if (index < text.length) {
          setAiTalkMessage(text.substring(0, index + 1));
          index++;
          typingTimer.current = setTimeout(typeChar, 50);
        }
      };
      
      typingTimer.current = setTimeout(typeChar, 300);
      
      return () => {
        if (typingTimer.current) {
          clearTimeout(typingTimer.current);
        }
      };
    }
  }, [currentAiTalk]);

  // ========== 粒子系统 ==========
  useEffect(() => {
    const initParticles = () => {
      const newParticles: Particle[] = [];
      const colors = moodColors[currentMood].particles;
      const particleTypes: Particle['type'][] = ['note', 'star', 'heart', 'wave'];
      
      for (let i = 0; i < 20; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * window.innerWidth,
          y: Math.random() * window.innerHeight,
          vx: (Math.random() - 0.5) * 0.2,
          vy: (Math.random() - 0.5) * 0.2,
          size: Math.random() * 3 + 1,
          opacity: Math.random() * 0.15 + 0.05,
          color: colors[Math.floor(Math.random() * colors.length)],
          type: particleTypes[Math.floor(Math.random() * particleTypes.length)],
          rotation: Math.random() * 360,
          rotationSpeed: (Math.random() - 0.5) * 2,
          lifespan: 100,
          maxLife: 100
        });
      }
      setParticles(newParticles);
    };

    initParticles();
  }, [currentMood]);

  // ========== 粒子动画循环 ==========
  useEffect(() => {
    const animate = () => {
      setParticles(prevParticles => 
        prevParticles.map(particle => {
          let newX = particle.x + particle.vx;
          let newY = particle.y + particle.vy;
          let newVx = particle.vx;
          let newVy = particle.vy;
          
          if (newX <= 0 || newX >= window.innerWidth) newVx = -newVx;
          if (newY <= 0 || newY >= window.innerHeight) newVy = -newVy;
          
          return {
            ...particle,
            x: newX,
            y: newY,
            vx: newVx,
            vy: newVy,
            rotation: particle.rotation + particle.rotationSpeed,
            opacity: particle.opacity + Math.sin(Date.now() * 0.001 + particle.id) * 0.01
          };
        })
      );
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // ========== AI交互处理 ==========
  const updateAiTalk = useCallback(() => {
    const hour = new Date().getHours();
    let talk: AITalk;
    
    if (hour >= 6 && hour < 12) {
      talk = {
        message: "早安！今天想来点什么音乐唤醒耳朵？",
        quickReplies: [
          { text: "元气满满", action: "morning_energy" },
          { text: "轻松惬意", action: "morning_calm" }
        ],
        mood: 'happy'
      };
    } else if (hour >= 22 || hour < 6) {
      talk = aiTalkLibrary[3];
    } else if (hour >= 12 && hour < 14) {
      talk = {
        message: "午休时间，来点舒缓的背景音乐？",
        quickReplies: [
          { text: "好的", action: "play_relax" },
          { text: "我要嗨歌", action: "play_energetic" }
        ],
        mood: 'calm'
      };
    } else {
      talk = aiTalkLibrary[Math.floor(Math.random() * aiTalkLibrary.length)];
    }
    
    setCurrentAiTalk(talk);
    if (talk.mood) {
      setCurrentMood(talk.mood);
    }
  }, []);

  const handleAiCommand = useCallback((command: string, fromQuickReply: boolean = false) => {
    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: fromQuickReply ? command.split('_').join(' ') : command,
      timestamp: new Date()
    };
    
    setAiMessages(prev => [...prev, userMessage]);
    setIsTyping(true);
    
    setTimeout(() => {
      let responseText = '';
      let suggestions: string[] = [];
      
      if (command.includes('love') || command.includes('表白')) {
        responseText = '已为你准备了最甜蜜的表白歌单，每一首都是心动的声音💕';
        suggestions = ['播放第一首', '查看歌词', '收藏歌单'];
      } else if (command.includes('happy') || command.includes('开心')) {
        responseText = '看来今天心情不错呢！给你来点欢快的音乐～';
        suggestions = ['随机播放', '查看更多'];
      } else if (command.includes('tired') || command.includes('累')) {
        responseText = '辛苦了！我给你准备了一些放松的音乐，好好休息一下吧';
        suggestions = ['播放轻音乐', '冥想音乐'];
      } else if (command.includes('sleep') || command.includes('助眠')) {
        responseText = '马上为你播放助眠音乐，祝你好梦～';
        suggestions = ['30分钟定时', '自然声音'];
      } else {
        responseText = `好的，正在为你${command}...`;
        suggestions = ['继续', '换一批'];
      }
      
      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: responseText,
        timestamp: new Date(),
        suggestions
      };
      
      setAiMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
      
      setTimeout(() => {
        if (chatScrollRef.current) {
          chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
        }
      }, 100);
      
      if (command.includes('播放') || command.includes('play')) {
        const randomCard = musicCards[Math.floor(Math.random() * musicCards.length)];
        handlePlayMusic(randomCard.id);
      }
    }, 1000);
  }, [musicCards]);

  const handleQuickReply = useCallback((reply: { text: string; action: string }) => {
    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: reply.text,
      timestamp: new Date()
    };
    setAiMessages(prev => [...prev, userMessage]);
    handleAiCommand(reply.action, true);
    setShowAiChat(true);
  }, [handleAiCommand]);

  const openChatWithContext = useCallback(() => {
    if (!hasInitialMessage) {
      const initialMessage: AIMessage = {
        id: 'initial-' + Date.now(),
        type: 'ai',
        content: currentAiTalk.message,
        timestamp: new Date(),
        suggestions: currentAiTalk.quickReplies?.map(r => r.text)
      };
      setAiMessages([initialMessage]);
      setHasInitialMessage(true);
    }
    setShowAiChat(true);
    setTimeout(() => inputRef.current?.focus(), 300);
  }, [currentAiTalk, hasInitialMessage]);

  const handleAiButtonTouchStart = useCallback(() => {
    longPressTimer.current = setTimeout(() => {
      setIsLongPressing(true);
      setShowQuickReply(true);
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, 300);
  }, []);

  const handleAiButtonTouchEnd = useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
    }
    
    if (!isLongPressing) {
      openChatWithContext();
    }
    
    setIsLongPressing(false);
    setTimeout(() => {
      setShowQuickReply(false);
    }, 200);
  }, [isLongPressing, openChatWithContext]);

  const handleAiButtonClick = useCallback(() => {
    if (!isLongPressing) {
      openChatWithContext();
    }
  }, [isLongPressing, openChatWithContext]);

  const handleAiSubmit = useCallback(() => {
    if (!aiInputText.trim()) return;
    handleAiCommand(aiInputText);
    setAiInputText('');
  }, [aiInputText, handleAiCommand]);

  // ========== 音乐控制 ==========
  const handlePlayMusic = useCallback((cardId: string) => {
    setCurrentPlayingId(prev => prev === cardId ? null : cardId);
  }, []);

  // ========== 渲染粒子形状 ==========
  const renderParticleShape = (type: Particle['type']) => {
    switch (type) {
      case 'note': return '♪';
      case 'star': return '✦';
      case 'heart': return '♥';
      case 'wave': return '~';
      default: return '•';
    }
  };

  // ==================== 主渲染 ====================
  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      {/* 手机框架 */}
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div className="relative w-full h-full bg-gray-900 rounded-[2.8rem] overflow-hidden">
          
          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <div className="flex items-center gap-1">
                <span className="font-medium">
                  {currentTime.getHours().toString().padStart(2, '0')}:
                  {currentTime.getMinutes().toString().padStart(2, '0')}
                </span>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="flex items-center gap-1">
                <Bell className="w-3.5 h-3.5 text-white/70" />
                <div className="w-6 h-3 border border-white/50 rounded-sm">
                  <div className="h-full bg-white rounded-sm" style={{ width: '85%' }} />
                </div>
              </div>
            </div>
          </div>

          {/* 动态背景层 */}
          <div className={`absolute inset-0 bg-gradient-to-br ${moodColors[currentMood].primary} opacity-10 transition-all duration-1000`} />
          
          {/* 粒子系统 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {particles.map((particle) => (
              <div
                key={particle.id}
                className="absolute animate-float-particle"
                style={{
                  left: `${particle.x}px`,
                  top: `${particle.y}px`,
                  transform: `translate(-50%, -50%) rotate(${particle.rotation}deg)`,
                  fontSize: `${particle.size * 4}px`,
                  color: particle.color,
                  opacity: particle.opacity,
                  filter: 'blur(0.5px)',
                  textShadow: `0 0 ${particle.size * 2}px ${particle.color}`
                }}
              >
                {renderParticleShape(particle.type)}
              </div>
            ))}
          </div>

          {/* AI语语区 */}
          <div className="relative pt-14 px-6 pb-3 z-20">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
              <div className="flex items-start gap-3 mb-3">
                <div className={`w-10 h-10 rounded-full bg-gradient-to-br ${moodColors[currentMood].primary} flex items-center justify-center flex-shrink-0 overflow-hidden`}>
                  <img 
                    src="/src/assets/xiaoxian.png" 
                    alt="小弦" 
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.parentElement?.insertAdjacentHTML('beforeend', '<div class="w-5 h-5 text-white">✨</div>');
                    }}
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-white font-medium text-sm">小弦</span>
                    <span className="text-xs text-white/40">AI音乐伙伴</span>
                  </div>
                  <p className="text-white/80 text-sm leading-relaxed">
                    {aiTalkMessage}
                    {aiTalkMessage.length < currentAiTalk.message.length && (
                      <span className="inline-block w-1 h-4 ml-1 bg-white/60 animate-blink" />
                    )}
                  </p>
                </div>
              </div>
              
              {currentAiTalk.quickReplies && aiTalkMessage === currentAiTalk.message && (
                <div className="flex gap-2 mt-3 animate-fade-in">
                  {currentAiTalk.quickReplies.map((reply, index) => (
                    <button
                      key={index}
                      className="px-4 py-1.5 bg-white/10 hover:bg-white/20 rounded-full text-white text-sm transition-all active:scale-95"
                      onClick={() => handleQuickReply(reply)}
                    >
                      {reply.text}
                    </button>
                  ))}
                </div>
              )}
              
              <div className="mt-3 flex items-center gap-1 text-white/40 text-xs">
                <span>💡</span>
                <span>长按右下角音律球可快速回复哦</span>
              </div>
            </div>
          </div>

          {/* 主内容区域 - 核心优化部分 */}
          <div className="absolute top-[240px] bottom-[80px] left-0 right-0 overflow-hidden px-6">
            {currentPage === 'home' && (
              <div className="h-full overflow-y-auto no-scrollbar">
                
                {/* 一键创作入口 - 场景化模板 */}
                <div className="mb-4">
                  <div className="grid grid-cols-4 gap-3">
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-pink-500/20 to-purple-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Heart className="w-5 h-5 text-pink-400" />
                      <span className="text-xs text-white/80">表白</span>
                    </button>
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Gift className="w-5 h-5 text-yellow-400" />
                      <span className="text-xs text-white/80">生日</span>
                    </button>
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Users className="w-5 h-5 text-blue-400" />
                      <span className="text-xs text-white/80">婚礼</span>
                    </button>
                    <button className="flex flex-col items-center gap-1 p-3 bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur rounded-xl hover:scale-105 transition-transform">
                      <Moon className="w-5 h-5 text-green-400" />
                      <span className="text-xs text-white/80">助眠</span>
                    </button>
                  </div>
                  <button className="mt-3 w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white font-medium flex items-center justify-center gap-2 hover:scale-[1.02] transition-transform">
                    <Feather className="w-5 h-5" />
                    <span>一键创作属于你的歌</span>
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>

                {/* 今日故事精选 - 突出故事性 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <BookOpen className="w-5 h-5 text-amber-400" />
                      今日故事精选
                    </h2>
                    <span className="text-xs text-white/60 bg-white/10 px-2 py-1 rounded-full">
                      每个故事都是一首歌
                    </span>
                  </div>
                  
                  <div className="space-y-3">
                    {storyCards.map((story) => (
                      <div key={story.id} className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 hover:bg-white/10 transition-all cursor-pointer">
                        <div className="flex gap-4">
                          <div className={`w-16 h-16 bg-gradient-to-br ${story.coverGradient} rounded-xl flex items-center justify-center flex-shrink-0`}>
                            <Music className="w-8 h-8 text-white/80" />
                          </div>
                          <div className="flex-1">
                            <h3 className="text-white font-medium text-sm mb-1">{story.title}</h3>
                            <p className="text-white/60 text-xs line-clamp-2 mb-2">{story.story}</p>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <span className="text-xs text-white/50">@{story.author}</span>
                                <div className="flex items-center gap-1">
                                  <Heart className="w-3 h-3 text-pink-400" />
                                  <span className="text-xs text-white/50">{story.resonanceCount}</span>
                                </div>
                              </div>
                              <button className="text-xs text-purple-400 hover:text-purple-300">
                                听Ta的故事 →
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 热门榜单入口 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <Trophy className="w-5 h-5 text-yellow-400" />
                      热门榜单
                    </h2>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3">
                    <button className="bg-gradient-to-br from-red-500/20 to-orange-500/20 backdrop-blur-lg rounded-xl p-4 hover:scale-105 transition-transform">
                      <Flame className="w-6 h-6 text-orange-400 mb-2" />
                      <p className="text-white font-medium text-sm">飙升榜</p>
                      <p className="text-white/50 text-xs mt-1">实时更新</p>
                    </button>
                    <button className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-lg rounded-xl p-4 hover:scale-105 transition-transform">
                      <Heart className="w-6 h-6 text-pink-400 mb-2" />
                      <p className="text-white font-medium text-sm">共鸣榜</p>
                      <p className="text-white/50 text-xs mt-1">最触动心弦</p>
                    </button>
                    <button className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-lg rounded-xl p-4 hover:scale-105 transition-transform">
                      <TrendingUp className="w-6 h-6 text-green-400 mb-2" />
                      <p className="text-white font-medium text-sm">新歌榜</p>
                      <p className="text-white/50 text-xs mt-1">发现新声音</p>
                    </button>
                  </div>
                </div>

                {/* 音乐接龙挑战 - 社交玩法 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <Users className="w-5 h-5 text-blue-400" />
                      音乐接龙挑战
                    </h2>
                    <button className="text-sm text-white/60 hover:text-white">
                      更多 <ChevronRight className="inline w-4 h-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    {chainChallenges.map((challenge) => (
                      <div key={challenge.id} className="bg-white/5 backdrop-blur-lg rounded-xl p-3 hover:bg-white/10 transition-all cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-12 h-12 bg-gradient-to-br ${challenge.coverGradient} rounded-lg flex items-center justify-center`}>
                              <Hash className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <p className="text-white font-medium text-sm">{challenge.title}</p>
                              <div className="flex items-center gap-3 mt-1">
                                <span className="text-xs text-white/50">{challenge.participants}人参与</span>
                                <span className="text-xs text-white/50">{challenge.segments}段接龙</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-xs text-purple-400 font-medium">{challenge.reward}</p>
                            <p className="text-xs text-white/40 mt-1">
                              还剩{Math.floor((challenge.deadline.getTime() - Date.now()) / (24 * 60 * 60 * 1000))}天
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 情感共鸣广场 - 分类音乐流 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <Sparkles className="w-5 h-5 text-purple-400" />
                      情感共鸣广场
                    </h2>
                  </div>
                  
                  <div className="flex gap-3 overflow-x-auto no-scrollbar pb-2">
                    <button className="flex-shrink-0 px-4 py-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur rounded-full text-white text-sm font-medium flex items-center gap-2">
                      <span>💔</span> 失恋疗伤
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">234在听</span>
                    </button>
                    <button className="flex-shrink-0 px-4 py-2 bg-white/10 backdrop-blur rounded-full text-white text-sm flex items-center gap-2">
                      <span>📚</span> 考研加油
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">189在听</span>
                    </button>
                    <button className="flex-shrink-0 px-4 py-2 bg-white/10 backdrop-blur rounded-full text-white text-sm flex items-center gap-2">
                      <span>💼</span> 职场解压
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">156在听</span>
                    </button>
                    <button className="flex-shrink-0 px-4 py-2 bg-white/10 backdrop-blur rounded-full text-white text-sm flex items-center gap-2">
                      <span>💕</span> 表白墙
                      <span className="text-xs bg-white/20 px-1.5 py-0.5 rounded-full">423在听</span>
                    </button>
                  </div>
                </div>

                {/* 为你推荐 - 个性化内容 */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-lg font-semibold text-white flex items-center gap-2">
                      <Sparkle className="w-5 h-5 text-yellow-400" />
                      为你推荐
                    </h2>
                    <button className="text-sm text-white/60 hover:text-white flex items-center gap-1">
                      <ArrowRight className="w-4 h-4" />
                      换一批
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {musicCards.slice(0, 6).map((card) => (
                      <div key={card.id} className="group">
                        <MusicCard 
                          card={card} 
                          isPlaying={currentPlayingId === card.id}
                          onPlay={() => handlePlayMusic(card.id)}
                        />
                        {/* 新增：显示共鸣和二创数据 */}
                        <div className="mt-2 flex items-center justify-between px-1">
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              <Heart className="w-3 h-3 text-pink-400" />
                              <span className="text-xs text-white/50">{card.resonanceCount}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <PenTool className="w-3 h-3 text-purple-400" />
                              <span className="text-xs text-white/50">{card.remixCount}</span>
                            </div>
                          </div>
                          <button className="text-xs text-purple-400 hover:text-purple-300 opacity-0 group-hover:opacity-100 transition-opacity">
                            二创
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* AI聊天覆盖层 - 保持半透明 */}
          {showAiChat && (
            <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-40 animate-fade-in">
              <div className="flex flex-col h-full pt-16">
                <div 
                  ref={chatScrollRef}
                  className="flex-1 overflow-y-auto px-6 py-4 space-y-4"
                >
                  {aiMessages.map(message => (
                    <div 
                      key={message.id}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.type === 'ai' && (
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center mr-2 flex-shrink-0">
                          <img 
                            src="/src/assets/xiaoxian.png" 
                            alt="小弦" 
                            className="w-full h-full object-cover rounded-full"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none';
                              e.currentTarget.parentElement?.insertAdjacentHTML('beforeend', '<div class="text-white text-xs">✨</div>');
                            }}
                          />
                        </div>
                      )}
                      
                      <div className={`max-w-[75%]`}>
                        <div className={`px-4 py-2 rounded-2xl ${
                          message.type === 'user' 
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                            : 'bg-white/10 text-white backdrop-blur'
                        }`}>
                          <p className="text-sm">{message.content}</p>
                        </div>
                        {message.suggestions && (
                          <div className="mt-2 flex gap-2 flex-wrap">
                            {message.suggestions.map((suggestion, i) => (
                              <button 
                                key={i}
                                className="px-3 py-1 bg-white/10 backdrop-blur rounded-full text-xs text-white/80 hover:bg-white/20 transition-all"
                                onClick={() => handleAiCommand(suggestion)}
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center mr-2">
                        <Sparkles className="w-4 h-4 text-white" />
                      </div>
                      <div className="bg-white/10 backdrop-blur px-4 py-2 rounded-2xl">
                        <div className="flex gap-1">
                          <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                          <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                          <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="border-t border-white/10 px-4 py-3 bg-black/30 backdrop-blur">
                  <div className="flex items-center gap-3">
                    <button 
                      className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center active:scale-95 hover:bg-white/20 transition-all"
                      onClick={() => setShowAiChat(false)}
                    >
                      <X className="w-5 h-5 text-white" />
                    </button>
                    <div className="flex-1 bg-white/10 backdrop-blur rounded-full px-4 py-2 flex items-center gap-2">
                      <input
                        ref={inputRef}
                        type="text"
                        value={aiInputText}
                        onChange={(e) => setAiInputText(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAiSubmit()}
                        placeholder="说点什么..."
                        className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                      />
                      <button 
                        className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center active:scale-95"
                        onClick={handleAiSubmit}
                      >
                        <Send className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 快捷回复悬浮层 */}
          {showQuickReply && (
            <div className="absolute bottom-24 right-4 z-50 animate-scale-up">
              <div className="bg-black/80 backdrop-blur-lg rounded-2xl p-3 border border-white/20">
                {currentAiTalk.quickReplies ? (
                  <div className="space-y-2">
                    {currentAiTalk.quickReplies.map((reply, index) => (
                      <button
                        key={index}
                        className="w-full px-4 py-2 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm text-left transition-all"
                        onClick={() => {
                          handleQuickReply(reply);
                          setShowQuickReply(false);
                        }}
                      >
                        {reply.text}
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center gap-3 px-3 py-2">
                    <Mic className="w-5 h-5 text-white animate-pulse" />
                    <span className="text-white text-sm">说出你的想法...</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 底部导航栏 */}
          <div className="absolute bottom-0 left-0 right-0 bg-black/30 backdrop-blur-xl border-t border-white/10">
            <div className="flex items-center justify-between px-6 py-3">
              <button 
                className={`flex flex-col items-center gap-1 py-1 transition-all ${
                  currentPage === 'home' ? 'text-white' : 'text-white/50'
                }`}
                onClick={() => setCurrentPage('home')}
              >
                <Home className="w-5 h-5" />
                <span className="text-xs">首页</span>
              </button>

              <div className="flex flex-col items-center gap-1 py-1 text-white/50">
                <PenTool className="w-5 h-5" />
                <span className="text-xs">创作</span>
              </div>

              <div className="flex flex-col items-center gap-1 py-1 text-white/50">
                <User className="w-5 h-5" />
                <span className="text-xs">我的</span>
              </div>

              <div className="relative">
                <div 
                  ref={aiButtonRef}
                  className={`relative transition-all duration-300 ${
                    isLongPressing ? 'scale-110' : ''
                  }`}
                  onTouchStart={handleAiButtonTouchStart}
                  onTouchEnd={handleAiButtonTouchEnd}
                  onMouseDown={handleAiButtonTouchStart}
                  onMouseUp={handleAiButtonTouchEnd}
                  onMouseLeave={handleAiButtonTouchEnd}
                  onClick={handleAiButtonClick}
                >
                  {aiButtonPulse && (
                    <div className="absolute -inset-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 animate-pulse-slow" />
                  )}
                  
                  <div className={`relative w-14 h-14 rounded-full overflow-hidden cursor-pointer ${
                    isLongPressing 
                      ? 'bg-gradient-to-br from-green-500 to-emerald-500' 
                      : 'bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500'
                  } shadow-2xl transform transition-all hover:scale-105`}>
                    <div className="absolute inset-0">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-full h-full animate-flow"
                          style={{
                            background: `radial-gradient(circle at ${30 + i * 20}% ${30 + i * 20}%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
                            animationDelay: `${i * 0.5}s`
                          }}
                        />
                      ))}
                    </div>
                    
                    <div className="relative w-full h-full flex items-center justify-center">
                      <div className="text-white text-2xl font-bold">✧</div>
                      <div className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-white animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 播放控制条 */}
          {currentPlayingId && (
            <div className="absolute bottom-20 left-4 right-4 bg-black/80 backdrop-blur-xl rounded-2xl p-3 z-30 animate-slide-up">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg flex items-center justify-center">
                  <Music className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <p className="text-white text-sm font-medium">
                    {musicCards.find(c => c.id === currentPlayingId)?.title}
                  </p>
                  <p className="text-white/60 text-xs">
                    {musicCards.find(c => c.id === currentPlayingId)?.artist}
                  </p>
                </div>
                <button 
                  className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center"
                  onClick={() => handlePlayMusic(currentPlayingId)}
                >
                  <Pause className="w-5 h-5 text-white" />
                </button>
                <button className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
                  <SkipForward className="w-5 h-5 text-white" />
                </button>
              </div>
              <div className="mt-2">
                <div className="h-1 bg-white/10 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-progress" />
                </div>
              </div>
            </div>
          )}

          {/* Home Indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        </div>
      </div>
    </div>
  );
};

// ==================== 子组件 ====================

// 音乐卡片组件
const MusicCard: React.FC<{
  card: MusicCard;
  isPlaying: boolean;
  onPlay: () => void;
}> = ({ card, isPlaying, onPlay }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className="relative cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onPlay}
    >
      {isPlaying && (
        <div className="absolute -inset-2 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-2xl animate-pulse" />
      )}
      
      <div className={`relative bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden transition-all duration-300 ${
        isHovered ? 'transform scale-105' : ''
      }`}>
        <div className={`absolute inset-0 bg-gradient-to-br ${card.coverGradient} opacity-30`} />
        
        {isPlaying && card.waveform && (
          <div className="absolute bottom-0 left-0 right-0 flex items-end justify-around h-20 px-2">
            {card.waveform.slice(0, 10).map((height, i) => (
              <div
                key={i}
                className="w-1 bg-white/40 rounded-full animate-wave"
                style={{
                  height: `${height * 0.5}%`,
                  animationDelay: `${i * 100}ms`
                }}
              />
            ))}
          </div>
        )}
        
        <div className="relative p-4">
          <div className="w-full aspect-square bg-gradient-to-br from-white/20 to-white/5 rounded-xl mb-3 flex items-center justify-center">
            <div className={`relative ${isPlaying ? 'animate-spin-slow' : ''}`}>
              <Music className="w-12 h-12 text-white/80" />
            </div>
          </div>
          
          <h3 className="text-white font-medium text-sm truncate">{card.title}</h3>
          <p className="text-white/60 text-xs truncate">{card.artist}</p>
          
          <div className="flex gap-1 mt-2">
            {card.tags.slice(0, 2).map((tag, i) => (
              <span key={i} className="text-xs bg-white/10 text-white/70 px-2 py-0.5 rounded-full">
                {tag}
              </span>
            ))}
          </div>
        </div>
        
        <div className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity ${
          isHovered ? 'opacity-100' : 'opacity-0'
        }`}>
          <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
            {isPlaying ? (
              <Pause className="w-6 h-6 text-gray-800" />
            ) : (
              <Play className="w-6 h-6 text-gray-800 ml-1" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// ==================== 样式注入 ====================
const style = document.createElement('style');
style.textContent = `
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
  
  @keyframes float-particle {
    0%, 100% { 
      transform: translate(-50%, -50%) translateY(0px) rotate(0deg);
    }
    50% { 
      transform: translate(-50%, -50%) translateY(-10px) rotate(180deg);
    }
  }
  
  @keyframes flow {
    0% { transform: translateY(100%) rotate(0deg); }
    100% { transform: translateY(-100%) rotate(360deg); }
  }
  
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slide-up {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes scale-up {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  
  @keyframes wave {
    0%, 100% { height: 20%; }
    50% { height: 60%; }
  }
  
  @keyframes progress {
    from { width: 0%; }
    to { width: 60%; }
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-particle {
    animation: float-particle 8s ease-in-out infinite;
  }
  
  .animate-flow {
    animation: flow 6s linear infinite;
  }
  
  .animate-spin-slow {
    animation: spin-slow 4s linear infinite;
  }
  
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.5s ease-out;
  }
  
  .animate-scale-up {
    animation: scale-up 0.3s ease-out;
  }
  
  .animate-wave {
    animation: wave 1.5s ease-in-out infinite;
  }
  
  .animate-progress {
    animation: progress 3s ease-out;
  }
  
  .animate-blink {
    animation: blink 1s infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  .animation-delay-100 {
    animation-delay: 100ms;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;
document.head.appendChild(style);

export default MusicHomePage;