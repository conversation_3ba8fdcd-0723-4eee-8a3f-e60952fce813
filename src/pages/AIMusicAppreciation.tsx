import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { 
  ArrowLeft, Play, Pause, Heart, MessageCircle, Share2, MoreVertical,
  Sparkles, ChevronUp, Send, X, Plus, Users, TrendingUp, 
  Music, Volume2, Skip<PERSON>orward, SkipBack, Repeat, Shuffle,
  Star, ThumbsUp, Bookmark, Download, ExternalLink, Gift,
  Zap, Clock, Hash, Edit3, Mic, MicOff, ChevronDown,
  AlertCircle, Check, Info, Headphones, Radio, PlusCircle
} from 'lucide-react';

// ==================== 类型定义 ====================
interface LyricLine {
  id: string;
  time: number; // 秒数
  text: string;
  resonanceCount: number;
  isResonated: boolean;
  comments: Comment[];
  translation?: string;
}

interface Comment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  timestamp: Date;
  likes: number;
  isLiked: boolean;
  lyricId?: string;
}

interface Song {
  id: string;
  title: string;
  artist: string;
  coverUrl: string;
  duration: number;
  plays: number;
  likes: number;
  shares: number;
  mood: string;
  moodEmoji: string;
  story: string;
  storyExpanded: boolean;
  allowRemix: boolean;
  createdAt: Date;
  tags: string[];
}

interface AIGuide {
  message: string;
  mood: 'happy' | 'excited' | 'calm' | 'encouraging' | 'curious';
  action?: string;
  suggestions?: string[];
}

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

// ==================== 模拟数据 ====================
const mockSong: Song = {
  id: 'song-1',
  title: '夏日晚风',
  artist: '月光诗人',
  coverUrl: '/cover.jpg',
  duration: 245,
  plays: 12580,
  likes: 3421,
  shares: 892,
  mood: 'romantic',
  moodEmoji: '💕',
  story: '这首歌诞生在一个夏天的傍晚，微风轻拂，夕阳西下。那时的我坐在海边，看着远方的地平线，心中涌起了对生活的无限感慨。希望这首歌能带给你同样的感动...',
  storyExpanded: false,
  allowRemix: true,
  createdAt: new Date('2024-08-15'),
  tags: ['治愈', '夏天', '海边', '晚风']
};

const mockLyrics: LyricLine[] = [
  { id: 'l1', time: 0, text: '当夏日的晚风轻轻吹过', resonanceCount: 156, isResonated: false, comments: [] },
  { id: 'l2', time: 4, text: '带走了白天所有的燥热', resonanceCount: 89, isResonated: false, comments: [] },
  { id: 'l3', time: 8, text: '我站在这里静静等候', resonanceCount: 234, isResonated: true, comments: [] },
  { id: 'l4', time: 12, text: '等待着你出现的时刻', resonanceCount: 412, isResonated: false, comments: [] },
  { id: 'l5', time: 16, text: '', resonanceCount: 0, isResonated: false, comments: [] },
  { id: 'l6', time: 20, text: '海浪拍打着礁石歌唱', resonanceCount: 178, isResonated: false, comments: [] },
  { id: 'l7', time: 24, text: '诉说着古老的故事', resonanceCount: 92, isResonated: false, comments: [] },
  { id: 'l8', time: 28, text: '星星开始在天空闪烁', resonanceCount: 356, isResonated: true, comments: [] },
  { id: 'l9', time: 32, text: '照亮了回家的路', resonanceCount: 267, isResonated: false, comments: [] },
  { id: 'l10', time: 36, text: '', resonanceCount: 0, isResonated: false, comments: [] },
  { id: 'l11', time: 40, text: '这个夏天有你真好', resonanceCount: 892, isResonated: true, comments: [] },
  { id: 'l12', time: 44, text: '所有美好都值得等待', resonanceCount: 634, isResonated: false, comments: [] },
  { id: 'l13', time: 48, text: '让我们一起走向远方', resonanceCount: 445, isResonated: false, comments: [] },
  { id: 'l14', time: 52, text: '把故事写进夏天的风里', resonanceCount: 723, isResonated: true, comments: [] },
];

const mockComments: Comment[] = [
  {
    id: 'c1',
    userId: 'u1',
    userName: '深夜听歌的人',
    content: '每次听到"星星开始在天空闪烁"这句，都会想起那个夏天的夜晚 ✨',
    timestamp: new Date('2024-08-20 22:30'),
    likes: 234,
    isLiked: false,
    lyricId: 'l8'
  },
  {
    id: 'c2',
    userId: 'u2',
    userName: '音乐收藏家',
    content: '这首歌真的太治愈了，循环了一整天都不腻！创作者的故事也很感人 💕',
    timestamp: new Date('2024-08-21 15:45'),
    likes: 189,
    isLiked: true,
    lyricId: undefined
  },
  {
    id: 'c3',
    userId: 'u3',
    userName: '夏日微风',
    content: '"这个夏天有你真好" - 献给所有在这个夏天遇见的美好的人',
    timestamp: new Date('2024-08-22 09:20'),
    likes: 567,
    isLiked: false,
    lyricId: 'l11'
  }
];

// ==================== 主组件 ====================
const MusicAppreciationPage: React.FC = () => {
  // ========== 状态管理 ==========
  const [currentSong] = useState<Song>(mockSong);
  const [lyrics] = useState<LyricLine[]>(mockLyrics);
  const [comments] = useState<Comment[]>(mockComments);
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [currentLyricIndex, setCurrentLyricIndex] = useState(0);
  const [showStoryDetail, setShowStoryDetail] = useState(false);
  const [showAllComments, setShowAllComments] = useState(false);
  const [resonatedLyrics, setResonatedLyrics] = useState<Set<string>>(new Set(['l3', 'l8', 'l11', 'l14']));
  const [showResonanceAnimation, setShowResonanceAnimation] = useState<string | null>(null);
  
  // AI相关状态
  const [aiGuide, setAiGuide] = useState<AIGuide>({
    message: "欢迎来到《夏日晚风》，这是一首充满回忆的歌曲。双击你喜欢的歌词，记录此刻的心情 ✨",
    mood: 'calm'
  });
  const [aiMessage, setAiMessage] = useState('');
  const [showAiChat, setShowAiChat] = useState(false);
  const [aiMessages, setAiMessages] = useState<AIMessage[]>([]);
  const [aiInputText, setAiInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showQuickComment, setShowQuickComment] = useState(false);
  const [selectedLyricForComment, setSelectedLyricForComment] = useState<LyricLine | null>(null);
  
  // 控制状态
  const [volume, setVolume] = useState(80);
  const [showVolumeControl, setShowVolumeControl] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [showRemixEntry, setShowRemixEntry] = useState(false);
  
  // Refs
  const lyricsContainerRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);
  const playbackTimer = useRef<NodeJS.Timeout | null>(null);
  const coverRef = useRef<HTMLDivElement>(null);

  // ========== 生命周期 ==========
  useEffect(() => {
    // 初始化播放
    if (isPlaying) {
      startPlayback();
    }
    
    return () => {
      if (playbackTimer.current) {
        clearInterval(playbackTimer.current);
      }
    };
  }, [isPlaying]);

  // 打字机效果
  useEffect(() => {
    if (aiGuide.message) {
      setAiMessage('');
      let index = 0;
      const text = aiGuide.message;
      
      const typeChar = () => {
        if (index < text.length) {
          setAiMessage(text.substring(0, index + 1));
          index++;
          typingTimer.current = setTimeout(typeChar, 30);
        }
      };
      
      typingTimer.current = setTimeout(typeChar, 300);
      
      return () => {
        if (typingTimer.current) {
          clearTimeout(typingTimer.current);
        }
      };
    }
  }, [aiGuide]);

  // 歌词同步滚动
  useEffect(() => {
    const currentLyric = lyrics.find((_, index) => {
      const nextLyric = lyrics[index + 1];
      return currentTime >= _.time && (!nextLyric || currentTime < nextLyric.time);
    });
    
    if (currentLyric) {
      const index = lyrics.indexOf(currentLyric);
      setCurrentLyricIndex(index);
      
      // 自动滚动到当前歌词
      if (lyricsContainerRef.current) {
        const lyricElement = lyricsContainerRef.current.children[index] as HTMLElement;
        if (lyricElement) {
          lyricElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    }
  }, [currentTime, lyrics]);

  // ========== 播放控制 ==========
  const startPlayback = useCallback(() => {
    playbackTimer.current = setInterval(() => {
      setCurrentTime(prev => {
        if (prev >= currentSong.duration) {
          setIsPlaying(false);
          handleSongEnd();
          return 0;
        }
        return prev + 0.1;
      });
    }, 100);
  }, [currentSong.duration]);

  const handleSongEnd = useCallback(() => {
    setAiGuide({
      message: "听完了吗？要不要看看其他听友的感受，或者开始你的二次创作？",
      mood: 'curious',
      suggestions: ['查看全部评论', '开始二创', '再听一遍']
    });
  }, []);

  const togglePlay = useCallback(() => {
    setIsPlaying(prev => !prev);
    if (!isPlaying && currentTime === 0) {
      setAiGuide({
        message: "让音乐带你回到那个夏天的傍晚... 记得双击你最有感触的歌词哦",
        mood: 'calm'
      });
    }
  }, [isPlaying, currentTime]);

  // ========== 歌词交互 ==========
  const handleLyricClick = useCallback((lyric: LyricLine) => {
    // 单击显示共鸣人数
    if (lyric.resonanceCount > 0) {
      setAiGuide({
        message: `已有 ${lyric.resonanceCount} 人在这句歌词产生了共鸣 💫`,
        mood: 'happy'
      });
    }
  }, []);

  const handleLyricDoubleClick = useCallback((lyric: LyricLine) => {
    if (lyric.text === '') return; // 空行不响应
    
    // 添加共鸣
    const newResonatedLyrics = new Set(resonatedLyrics);
    if (!newResonatedLyrics.has(lyric.id)) {
      newResonatedLyrics.add(lyric.id);
      setResonatedLyrics(newResonatedLyrics);
      
      // 显示+1动画
      setShowResonanceAnimation(lyric.id);
      setTimeout(() => setShowResonanceAnimation(null), 1000);
      
      // 更新AI引导
      setAiGuide({
        message: `共鸣成功！想为"${lyric.text.substring(0, 10)}..."写点什么吗？`,
        mood: 'excited',
        action: 'write_comment',
        suggestions: ['写评论', '看看其他人的感受', '继续听歌']
      });
      
      // 显示快速评论入口
      setSelectedLyricForComment(lyric);
      setShowQuickComment(true);
    }
  }, [resonatedLyrics]);

  // ========== AI交互 ==========
  const handleAiButtonClick = useCallback(() => {
    setShowAiChat(true);
    if (aiMessages.length === 0) {
      const initialMessage: AIMessage = {
        id: 'ai-1',
        type: 'ai',
        content: '我是小弦，你的AI音乐伙伴！有什么想和我聊的吗？比如这首歌的创作背景、歌词含义，或者你的感受...',
        timestamp: new Date(),
        suggestions: ['歌曲解析', '创作背景', '推荐相似歌曲']
      };
      setAiMessages([initialMessage]);
    }
  }, [aiMessages]);

  const handleAiSubmit = useCallback(() => {
    if (!aiInputText.trim()) return;
    
    // 添加用户消息
    const userMessage: AIMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: aiInputText,
      timestamp: new Date()
    };
    
    setAiMessages(prev => [...prev, userMessage]);
    setAiInputText('');
    setIsTyping(true);
    
    // 模拟AI回复
    setTimeout(() => {
      let aiResponse = '';
      if (aiInputText.includes('歌词') || aiInputText.includes('意思')) {
        aiResponse = '这首歌的歌词描绘了一个夏日傍晚的美好场景。"晚风"象征着轻松惬意，"星星闪烁"代表希望和美好。整首歌传达了珍惜当下、感恩相遇的情感。';
      } else if (aiInputText.includes('创作') || aiInputText.includes('背景')) {
        aiResponse = '创作者说这首歌诞生在海边，那个夏天的傍晚给了他/她无限灵感。微风、夕阳、海浪声，这些自然元素都融入到了音乐中。';
      } else if (aiInputText.includes('二创') || aiInputText.includes('改编')) {
        aiResponse = '这首歌开放了二创权限！你可以尝试改编成不同的风格，比如电子、爵士或者民谣版本。需要我帮你开始二创吗？';
      } else {
        aiResponse = `我明白你的感受。音乐总能触动我们内心最柔软的地方。关于"${aiInputText.substring(0, 20)}"，你想深入聊聊吗？`;
      }
      
      const aiMessage: AIMessage = {
        id: `ai-${Date.now()}`,
        type: 'ai',
        content: aiResponse,
        timestamp: new Date()
      };
      
      setAiMessages(prev => [...prev, aiMessage]);
      setIsTyping(false);
    }, 1500);
  }, [aiInputText]);

  const handleQuickCommentSubmit = useCallback((commentText: string) => {
    if (!selectedLyricForComment) return;
    
    // 模拟提交评论
    setAiGuide({
      message: '评论发布成功！你的感受已经被记录下来了 💝',
      mood: 'happy'
    });
    
    setShowQuickComment(false);
    setSelectedLyricForComment(null);
  }, [selectedLyricForComment]);

  // ========== 其他交互 ==========
  const handleLike = useCallback(() => {
    setIsLiked(!isLiked);
    if (!isLiked) {
      setAiGuide({
        message: '感谢你的喜欢！这会让创作者更有动力 ❤️',
        mood: 'happy'
      });
    }
  }, [isLiked]);

  const handleBookmark = useCallback(() => {
    setIsBookmarked(!isBookmarked);
    if (!isBookmarked) {
      setAiGuide({
        message: '已收藏！下次可以在"我的收藏"中快速找到',
        mood: 'calm'
      });
    }
  }, [isBookmarked]);

  const handleShare = useCallback(() => {
    setShowShareOptions(!showShareOptions);
  }, [showShareOptions]);

  const handleBack = useCallback(() => {
    // 返回上一页逻辑
    console.log('返回上一页');
  }, []);

  // ========== 渲染方法 ==========
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // ==================== 主渲染 ====================
  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      {/* 手机框架 */}
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div className="relative w-full h-full bg-gray-900 rounded-[2.8rem] overflow-hidden flex flex-col">
          
          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <div className="flex items-center gap-1">
                <span className="font-medium">9:41</span>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="flex items-center gap-1">
                <div className="w-4 h-4 border border-white/50 rounded-sm">
                  <div className="h-full bg-white rounded-sm" style={{ width: '70%' }} />
                </div>
              </div>
            </div>
          </div>

          {/* 背景渐变 */}
          <div className="absolute inset-0 bg-gradient-to-b from-purple-900/20 via-blue-900/20 to-gray-900 pointer-events-none" />

          {/* AI灵感丝带 - 固定在顶部 */}
          <div className="px-6 pt-14 pb-3 z-20">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-3 border border-white/10">
              <div className="flex items-start gap-2.5">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center flex-shrink-0">
                  <Sparkles className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-0.5">
                    <span className="text-white font-medium text-xs">小弦</span>
                    <span className="text-xs text-white/40">AI音乐伙伴</span>
                  </div>
                  <p className="text-white/80 text-xs leading-relaxed">
                    {aiMessage}
                    {aiMessage.length < aiGuide.message.length && (
                      <span className="inline-block w-0.5 h-3 ml-0.5 bg-white/60 animate-blink" />
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 主内容区 - 可滚动 */}
          <div className="flex-1 overflow-y-auto no-scrollbar">
            <div className="px-6 pb-32">
              {/* 播放器区域 (40%) */}
              <div className="mb-6">
                {/* 封面展示 */}
                <div 
                  ref={coverRef}
                  className="relative w-full aspect-square bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500 rounded-2xl mb-4 overflow-hidden group"
                >
                  {/* 动态波形效果 */}
                  {isPlaying && (
                    <div className="absolute bottom-0 left-0 right-0 flex items-end justify-around h-32 px-4">
                      {Array.from({ length: 30 }, (_, i) => (
                        <div
                          key={i}
                          className="w-1 bg-white/30 rounded-full animate-wave"
                          style={{
                            height: `${Math.random() * 60 + 20}%`,
                            animationDelay: `${i * 50}ms`,
                            animationDuration: '1.5s'
                          }}
                        />
                      ))}
                    </div>
                  )}
                  
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                    <Music className="w-24 h-24 text-white/30" />
                  </div>
                  
                  {/* 长按提示 */}
                  <div className="absolute top-4 right-4 bg-black/50 backdrop-blur rounded-full px-3 py-1.5 text-white/70 text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                    长按查看动画
                  </div>
                </div>

                {/* 基础信息 */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h1 className="text-white text-xl font-semibold mb-3">{currentSong.title}</h1>
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{currentSong.moodEmoji}</span>
                      <div className="flex gap-1">
                        {currentSong.tags.map((tag, i) => (
                          <span key={i} className="text-xs bg-white/10 text-white/60 px-2 py-0.5 rounded-full">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  <button className="p-2 hover:bg-white/10 rounded-full transition-all">
                    <MoreVertical className="w-5 h-5 text-white/60" />
                  </button>
                </div>

                {/* 播放控制 */}
                <div className="space-y-4">
                  {/* 进度条 */}
                  <div>
                    <div 
                      ref={progressRef}
                      className="h-1 bg-white/10 rounded-full overflow-hidden cursor-pointer"
                      onClick={(e) => {
                        const rect = e.currentTarget.getBoundingClientRect();
                        const percent = (e.clientX - rect.left) / rect.width;
                        setCurrentTime(currentSong.duration * percent);
                      }}
                    >
                      <div 
                        className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full transition-all"
                        style={{ width: `${(currentTime / currentSong.duration) * 100}%` }}
                      />
                    </div>
                    <div className="flex justify-between mt-1 text-xs text-white/40">
                      <span>{formatTime(currentTime)}</span>
                      <span>{formatTime(currentSong.duration)}</span>
                    </div>
                  </div>

                  {/* 快速操作 */}
                  <div className="flex items-center justify-around">
                    <button 
                      className={`p-2 rounded-full transition-all ${isLiked ? 'text-pink-400' : 'text-white/60'} hover:bg-white/10`}
                      onClick={handleLike}
                    >
                      <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                    </button>
                    <button 
                      className="p-2 rounded-full text-white/60 hover:bg-white/10 transition-all"
                      onClick={handleShare}
                    >
                      <Share2 className="w-5 h-5" />
                    </button>
                    <button 
                      className="flex items-center gap-1 px-3 py-2 rounded-full text-white/60 hover:bg-white/10 transition-all"
                      onClick={() => setShowRemixEntry(true)}
                    >
                      <Edit3 className="w-4 h-4" />
                      <span className="text-xs">二创</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* 歌词互动区 (40%) */}
              <div className="mb-6">
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
                  <div className="flex items-center justify-between mb-3">
                    <h2 className="text-white font-medium text-sm">歌词</h2>
                    <span className="text-xs text-white/40">双击标记共鸣</span>
                  </div>
                  
                  <div 
                    ref={lyricsContainerRef}
                    className="h-64 overflow-y-auto no-scrollbar space-y-3"
                  >
                    {lyrics.map((lyric, index) => (
                      <div
                        key={lyric.id}
                        className={`relative px-3 py-2 rounded-lg transition-all cursor-pointer ${
                          index === currentLyricIndex 
                            ? 'bg-white/10 scale-105' 
                            : 'hover:bg-white/5'
                        } ${lyric.text === '' ? 'cursor-default' : ''}`}
                        onClick={() => handleLyricClick(lyric)}
                        onDoubleClick={() => handleLyricDoubleClick(lyric)}
                      >
                        <div className="flex items-center justify-between">
                          <p className={`text-sm transition-all ${
                            index === currentLyricIndex 
                              ? 'text-white font-medium' 
                              : resonatedLyrics.has(lyric.id)
                              ? 'text-purple-300'
                              : 'text-white/60'
                          }`}>
                            {lyric.text || '\u00A0'}
                          </p>
                          
                          {lyric.resonanceCount > 0 && (
                            <div className="flex items-center gap-1">
                              <span className="text-xs text-white/40">
                                {lyric.resonanceCount}
                              </span>
                              {resonatedLyrics.has(lyric.id) && (
                                <Heart className="w-3 h-3 text-pink-400 fill-current" />
                              )}
                            </div>
                          )}
                        </div>
                        
                        {/* 共鸣动画 */}
                        {showResonanceAnimation === lyric.id && (
                          <div className="absolute top-0 right-4 text-pink-400 font-bold animate-float-up">
                            +1
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 底部信息区 (20%) */}
              <div className="space-y-4">
                {/* 创作故事 */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
                  <div 
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => setShowStoryDetail(!showStoryDetail)}
                  >
                    <h3 className="text-white font-medium text-sm">创作故事</h3>
                    <ChevronDown className={`w-4 h-4 text-white/60 transition-transform ${
                      showStoryDetail ? 'rotate-180' : ''
                    }`} />
                  </div>
                  
                  {/* 作者信息 */}
                  <div className="flex items-center gap-3 mt-3 mb-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center flex-shrink-0">
                      <Music className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white font-medium text-sm">{currentSong.artist}</p>
                      <p className="text-white/40 text-xs">创作者</p>
                    </div>
                  </div>
                  
                  <p className={`text-white/60 text-sm ${
                    showStoryDetail ? '' : 'line-clamp-2'
                  }`}>
                    {currentSong.story}
                  </p>
                  {showStoryDetail && (
                    <button className="mt-3 text-xs text-purple-400 hover:text-purple-300 flex items-center gap-1">
                      <MessageCircle className="w-3 h-3" />
                      对故事发表评论
                    </button>
                  )}
                </div>

                {/* 热门评论 */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-white font-medium text-sm">热门评论</h3>
                    <button 
                      className="text-xs text-purple-400 hover:text-purple-300"
                      onClick={() => setShowAllComments(true)}
                    >
                      查看全部
                    </button>
                  </div>
                  
                  <div className="space-y-3">
                    {comments.slice(0, 2).map(comment => (
                      <div key={comment.id} className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex-shrink-0" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs text-white/80 font-medium">{comment.userName}</span>
                            {comment.lyricId && (
                              <span className="text-xs text-purple-400">
                                @{lyrics.find(l => l.id === comment.lyricId)?.text.substring(0, 10)}...
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-white/60 leading-relaxed">{comment.content}</p>
                          <div className="flex items-center gap-3 mt-1">
                            <button className={`flex items-center gap-1 text-xs ${
                              comment.isLiked ? 'text-pink-400' : 'text-white/40'
                            }`}>
                              <ThumbsUp className="w-3 h-3" />
                              {comment.likes}
                            </button>
                            <span className="text-xs text-white/40">
                              {new Date(comment.timestamp).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 二创入口 */}
                {currentSong.allowRemix && (
                  <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-lg rounded-2xl p-4 border border-purple-500/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                          <Edit3 className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="text-white font-medium text-sm">开启二次创作</h3>
                          <p className="text-white/60 text-xs">用你的方式演绎这首歌</p>
                        </div>
                      </div>
                      <button 
                        className="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white text-sm font-medium hover:scale-105 transition-transform"
                        onClick={() => setShowRemixEntry(true)}
                      >
                        开始创作
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 快速评论弹窗 */}
          {showQuickComment && selectedLyricForComment && (
            <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-40 flex items-end animate-fade-in">
              <div className="w-full bg-gray-900 rounded-t-3xl p-6 animate-slide-up">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-white font-medium">为这句歌词写评论</h3>
                  <button 
                    onClick={() => setShowQuickComment(false)}
                    className="p-2 hover:bg-white/10 rounded-full transition-all"
                  >
                    <X className="w-5 h-5 text-white/60" />
                  </button>
                </div>
                
                <div className="bg-white/10 rounded-xl p-3 mb-4">
                  <p className="text-white/80 text-sm italic">"{selectedLyricForComment.text}"</p>
                </div>
                
                <div className="flex gap-2 mb-4">
                  <button className="px-3 py-1.5 bg-white/10 rounded-full text-xs text-white/70 hover:bg-white/20">
                    太有感觉了
                  </button>
                  <button className="px-3 py-1.5 bg-white/10 rounded-full text-xs text-white/70 hover:bg-white/20">
                    想起了...
                  </button>
                  <button className="px-3 py-1.5 bg-white/10 rounded-full text-xs text-white/70 hover:bg-white/20">
                    单曲循环中
                  </button>
                </div>
                
                <div className="flex gap-3">
                  <input
                    type="text"
                    placeholder="说点什么..."
                    className="flex-1 bg-white/10 rounded-full px-4 py-3 text-white placeholder-white/40 outline-none focus:bg-white/15 transition-all"
                    autoFocus
                  />
                  <button 
                    className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-white font-medium hover:scale-105 transition-transform"
                    onClick={() => handleQuickCommentSubmit('评论内容')}
                  >
                    发送
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* AI聊天覆盖层 */}
          {showAiChat && (
            <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-40 animate-fade-in">
              <div className="flex flex-col h-full pt-16">
                {/* 聊天内容 */}
                <div className="flex-1 overflow-y-auto px-6 py-4 space-y-4">
                  {aiMessages.map(message => (
                    <div 
                      key={message.id}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.type === 'ai' && (
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center mr-2 flex-shrink-0">
                          <Sparkles className="w-4 h-4 text-white" />
                        </div>
                      )}
                      
                      <div className={`max-w-[75%]`}>
                        <div className={`px-4 py-2 rounded-2xl ${
                          message.type === 'user' 
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                            : 'bg-white/10 text-white backdrop-blur'
                        }`}>
                          <p className="text-sm">{message.content}</p>
                        </div>
                        {message.suggestions && (
                          <div className="mt-2 flex gap-2 flex-wrap">
                            {message.suggestions.map((suggestion, i) => (
                              <button 
                                key={i}
                                className="px-3 py-1 bg-white/10 backdrop-blur rounded-full text-xs text-white/80 hover:bg-white/20 transition-all"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {isTyping && (
                    <div className="flex justify-start">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center mr-2">
                        <Sparkles className="w-4 h-4 text-white" />
                      </div>
                      <div className="bg-white/10 backdrop-blur px-4 py-2 rounded-2xl">
                        <div className="flex gap-1">
                          <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                          <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                          <span className="w-2 h-2 bg-white/60 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 输入区域 */}
                <div className="border-t border-white/10 px-4 py-3 bg-black/30 backdrop-blur">
                  <div className="flex items-center gap-3">
                    <button 
                      className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-all"
                      onClick={() => setShowAiChat(false)}
                    >
                      <X className="w-5 h-5 text-white" />
                    </button>
                    <div className="flex-1 bg-white/10 backdrop-blur rounded-full px-4 py-2 flex items-center gap-2">
                      <input
                        type="text"
                        value={aiInputText}
                        onChange={(e) => setAiInputText(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAiSubmit()}
                        placeholder="说点什么..."
                        className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                      />
                      <button 
                        className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center hover:scale-105 transition-transform"
                        onClick={handleAiSubmit}
                      >
                        <Send className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 底部固定操作栏 */}
          <div className="absolute bottom-0 left-0 right-0 z-30 px-6 pb-8 pt-4 bg-gradient-to-t from-gray-900 via-gray-900/95 to-transparent">
            <div className="flex items-center gap-3">
              {/* 返回按钮 */}
              <button
                onClick={handleBack}
                className="w-14 h-14 bg-white/10 hover:bg-white/15 backdrop-blur-lg rounded-full flex items-center justify-center transition-all hover:scale-105 active:scale-95"
              >
                <ArrowLeft className="w-6 h-6 text-white" />
              </button>
              
              {/* 播放控制区 */}
              <div className="flex-1 h-14 bg-white/10 backdrop-blur-lg rounded-full px-4 flex items-center justify-between">
                <button className="p-2 hover:bg-white/10 rounded-full transition-all">
                  <SkipBack className="w-5 h-5 text-white" />
                </button>
                <button 
                  className="p-3 bg-white/20 hover:bg-white/30 rounded-full transition-all"
                  onClick={togglePlay}
                >
                  {isPlaying ? (
                    <Pause className="w-5 h-5 text-white" />
                  ) : (
                    <Play className="w-5 h-5 text-white ml-0.5" />
                  )}
                </button>
                <button className="p-2 hover:bg-white/10 rounded-full transition-all">
                  <SkipForward className="w-5 h-5 text-white" />
                </button>
                <div className="flex items-center gap-2">
                  <button 
                    className="p-2 hover:bg-white/10 rounded-full transition-all"
                    onClick={() => setShowVolumeControl(!showVolumeControl)}
                  >
                    <Volume2 className="w-4 h-4 text-white/60" />
                  </button>
                  {showVolumeControl && (
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={volume}
                      onChange={(e) => setVolume(Number(e.target.value))}
                      className="w-20 accent-purple-500"
                    />
                  )}
                </div>
              </div>
              
              {/* 音律珠 - AI助手 */}
              <button
                onClick={handleAiButtonClick}
                className="relative group"
              >
                {/* 呼吸光环 */}
                <div className="absolute -inset-1 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 animate-pulse-slow" />
                
                {/* 音律珠主体 */}
                <div className="relative w-14 h-14 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500 shadow-2xl transform transition-all hover:scale-110">
                  {/* 内部流动效果 */}
                  <div className="absolute inset-0">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="absolute w-full h-full animate-flow"
                        style={{
                          background: `radial-gradient(circle at ${30 + i * 20}% ${30 + i * 20}%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
                          animationDelay: `${i * 0.5}s`
                        }}
                      />
                    ))}
                  </div>
                  
                  {/* 中心符号 */}
                  <div className="relative w-full h-full flex items-center justify-center">
                    <Sparkles className="w-6 h-6 text-white" />
                    {/* 状态指示点 */}
                    <div className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-white animate-pulse" />
                  </div>
                </div>
                
                {/* 提示文字 */}
                <div className="absolute -top-8 right-0 bg-black/80 text-white text-xs px-2 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  AI助手
                </div>
              </button>
            </div>
          </div>

          {/* Home Indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        </div>
      </div>
    </div>
  );
};

// ==================== 样式注入 ====================
const style = document.createElement('style');
style.textContent = `
  @keyframes wave {
    0%, 100% { transform: scaleY(0.5); }
    50% { transform: scaleY(1); }
  }
  
  @keyframes flow {
    0% { transform: translateY(100%) rotate(0deg); }
    100% { transform: translateY(-100%) rotate(360deg); }
  }
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slide-up {
    from { transform: translateY(100px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  @keyframes float-up {
    from { 
      transform: translateY(0) scale(1); 
      opacity: 1; 
    }
    to { 
      transform: translateY(-30px) scale(1.5); 
      opacity: 0; 
    }
  }
  
  @keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
  }
  
  .animate-wave {
    animation: wave 1.5s ease-in-out infinite;
  }
  
  .animate-flow {
    animation: flow 6s linear infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  .animate-blink {
    animation: blink 1s infinite;
  }
  
  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-up 0.3s ease-out;
  }
  
  .animate-float-up {
    animation: float-up 1s ease-out forwards;
  }
  
  .animate-bounce {
    animation: bounce 0.6s ease-in-out infinite;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 自定义滑块样式 */
  input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
  }
  
  input[type="range"]::-webkit-slider-track {
    background: rgba(255, 255, 255, 0.1);
    height: 4px;
    border-radius: 2px;
  }
  
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    background: white;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    margin-top: -4px;
  }
  
  input[type="range"]::-moz-range-track {
    background: rgba(255, 255, 255, 0.1);
    height: 4px;
    border-radius: 2px;
  }
  
  input[type="range"]::-moz-range-thumb {
    background: white;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    border: none;
  }
`;
document.head.appendChild(style);

export default MusicAppreciationPage;