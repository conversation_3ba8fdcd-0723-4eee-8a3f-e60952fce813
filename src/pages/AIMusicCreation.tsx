import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { 
  <PERSON>, Sparkles, Play, Heart, Share2, ArrowLeft,
  Home, Volume2, Pause, Send, User, Camera, Mic,
  X, ChevronDown, ChevronUp, Loader2,
  PenTool, Zap, Clock, Gift, Cake, HeartCrack, Moon,
  Coffee, Briefcase, Sun, Cloud, Star,
  Check, AlertCircle, Headphones, SlidersHorizontal,
  FileText, Image, Video, Hash, TrendingUp, Shuffle,
  Plus, Minus, ChevronRight, MoreVertical, Layers,
  Bell, <PERSON><PERSON><PERSON><PERSON>,
  Piano, Guitar, Drum, Radio, Settings
} from 'lucide-react';

// ==================== 类型定义 ====================
interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  opacity: number;
  color: string;
  type: 'note' | 'star' | 'heart' | 'wave';
  rotation: number;
  rotationSpeed: number;
}

interface MoodColor {
  primary: string;
  secondary: string;
  particles: string[];
  glow: string;
  background: string;
  accent: string;
}

interface SceneTemplate {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  emoji: string;
  prompt: string;
  mood: string;
  style: string[];
  description: string;
  color: string;
}

interface CreationMode {
  type: 'simple' | 'professional';
  label: string;
  description: string;
}

interface GenerationState {
  isGenerating: boolean;
  progress: number;
  message: string;
  result?: GeneratedMusic;
}

interface GeneratedMusic {
  id: string;
  title: string;
  duration: string;
  waveform: number[];
  coverGradient: string;
  mood: string;
  style: string[];
  lyrics?: string[];
  bpm?: number;
}

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface InputMethod {
  type: 'text' | 'voice' | 'image' | 'video';
  icon: React.ComponentType<any>;
  label: string;
  active: boolean;
}

interface MusicParameter {
  name: string;
  value: any;
  type: 'select' | 'slider' | 'multi-select' | 'toggle';
  options?: any[];
}

interface AITalk {
  message: string;
  quickReplies?: { text: string; action: string }[];
  mood?: 'happy' | 'excited' | 'calm' | 'curious' | 'sad' | 'romantic';
}

// ==================== 配色系统 ====================
const moodColors: Record<string, MoodColor> = {
  happy: {
    primary: 'from-yellow-400 via-amber-400 to-orange-400',
    secondary: 'from-amber-300 to-yellow-300',
    particles: ['#FCD34D', '#FB923C', '#FBBF24', '#F59E0B'],
    glow: 'shadow-yellow-400/50',
    background: 'from-amber-50 to-orange-50',
    accent: '#F59E0B'
  },
  calm: {
    primary: 'from-blue-400 via-cyan-400 to-teal-400',
    secondary: 'from-teal-300 to-blue-300',
    particles: ['#60A5FA', '#06B6D4', '#14B8A6', '#10B981'],
    glow: 'shadow-cyan-400/50',
    background: 'from-blue-50 to-cyan-50',
    accent: '#06B6D4'
  },
  sad: {
    primary: 'from-slate-400 via-gray-400 to-zinc-400',
    secondary: 'from-gray-300 to-slate-300',
    particles: ['#94A3B8', '#9CA3AF', '#A1A1AA', '#71717A'],
    glow: 'shadow-gray-400/50',
    background: 'from-gray-50 to-slate-50',
    accent: '#6B7280'
  },
  excited: {
    primary: 'from-purple-400 via-pink-400 to-rose-400',
    secondary: 'from-rose-300 to-purple-300',
    particles: ['#C084FC', '#F472B6', '#EC4899', '#DB2777'],
    glow: 'shadow-pink-400/50',
    background: 'from-purple-50 to-pink-50',
    accent: '#EC4899'
  },
  romantic: {
    primary: 'from-pink-400 via-red-400 to-rose-400',
    secondary: 'from-rose-300 to-pink-300',
    particles: ['#FB7185', '#F87171', '#FCA5A5', '#FDA4AF'],
    glow: 'shadow-rose-400/50',
    background: 'from-pink-50 to-rose-50',
    accent: '#F43F5E'
  },
  curious: {
    primary: 'from-indigo-400 via-purple-400 to-violet-400',
    secondary: 'from-violet-300 to-indigo-300',
    particles: ['#818CF8', '#A78BFA', '#C084FC', '#E879F9'],
    glow: 'shadow-purple-400/50',
    background: 'from-indigo-50 to-purple-50',
    accent: '#A78BFA'
  }
};

// ==================== AI语话库 ====================
const aiTalkLibrary: AITalk[] = [
  {
    message: "第一次创作？我来帮你！试试下方的表白模板 💕",
    quickReplies: [
      { text: "开始创作", action: "start_create" },
      { text: "看看模板", action: "view_templates" }
    ],
    mood: 'happy'
  },
  {
    message: "🔥 #夏日限定# 挑战赛正在进行，一键参与！",
    quickReplies: [
      { text: "参加挑战", action: "join_challenge" },
      { text: "看看作品", action: "view_works" }
    ],
    mood: 'excited'
  },
  {
    message: "深夜了，适合创作一些安静的旋律 🌙",
    quickReplies: [
      { text: "助眠音乐", action: "sleep_music" },
      { text: "深夜独白", action: "night_talk" }
    ],
    mood: 'calm'
  },
  {
    message: "今天想创作什么样的音乐呢？💫",
    quickReplies: [
      { text: "表白情歌", action: "love_song" },
      { text: "治愈系", action: "healing" }
    ],
    mood: 'curious'
  },
  {
    message: "检测到忧伤情绪，已自动选择小调 🎵",
    quickReplies: [
      { text: "继续创作", action: "continue" },
      { text: "换个心情", action: "change_mood" }
    ],
    mood: 'sad'
  },
  {
    message: "最近大家都在创作毕业季主题曲 🎓",
    quickReplies: [
      { text: "我也要", action: "graduation" },
      { text: "听听看", action: "listen" }
    ],
    mood: 'romantic'
  }
];

// ==================== 场景模板数据 ====================
const sceneTemplates: SceneTemplate[] = [
  {
    id: 'confession',
    name: '表白',
    icon: Heart,
    emoji: '💕',
    prompt: '想对[TA的名字]说...',
    mood: 'romantic',
    style: ['流行', '抒情'],
    description: '勇敢说出心里话',
    color: 'from-pink-400 to-rose-400'
  },
  {
    id: 'birthday',
    name: '生日',
    icon: Cake,
    emoji: '🎂',
    prompt: '祝[名字]生日快乐...',
    mood: 'happy',
    style: ['欢快', '祝福'],
    description: '定制生日祝福歌',
    color: 'from-yellow-400 to-orange-400'
  },
  {
    id: 'breakup',
    name: '失恋',
    icon: HeartCrack,
    emoji: '💔',
    prompt: '分手第[N]天，还是会想起...',
    mood: 'sad',
    style: ['抒情', '伤感'],
    description: '治愈失恋的心',
    color: 'from-gray-400 to-slate-400'
  },
  {
    id: 'sleep',
    name: '助眠',
    icon: Moon,
    emoji: '🌙',
    prompt: '今夜无眠，让音乐陪伴...',
    mood: 'calm',
    style: ['轻音乐', '冥想'],
    description: '安静入眠好梦',
    color: 'from-indigo-400 to-purple-400'
  },
  {
    id: 'work',
    name: '加班',
    icon: Coffee,
    emoji: '☕',
    prompt: '凌晨[时间]，办公室只剩我...',
    mood: 'calm',
    style: ['解压', '轻松'],
    description: '解压吐槽神曲',
    color: 'from-amber-400 to-brown-400'
  }
];

// ==================== 主组件 ====================
const MusicCreationPage: React.FC = () => {
  // ========== 状态管理 ==========
  const [currentMood, setCurrentMood] = useState<string>('excited');
  const [particles, setParticles] = useState<Particle[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [creationMode, setCreationMode] = useState<'simple' | 'professional'>('simple');
  const [inputText, setInputText] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<SceneTemplate | null>(null);
  const [generationState, setGenerationState] = useState<GenerationState>({
    isGenerating: false,
    progress: 0,
    message: ''
  });
  
  // AI相关状态
  const [aiMessages, setAiMessages] = useState<AIMessage[]>([]);
  const [showAiChat, setShowAiChat] = useState(false);
  const [aiInputText, setAiInputText] = useState('');
  const [currentAiTalk, setCurrentAiTalk] = useState<AITalk>(aiTalkLibrary[0]);
  const [aiTalkMessage, setAiTalkMessage] = useState('');
  const [showQuickReply, setShowQuickReply] = useState(false);
  const [aiButtonPulse, setAiButtonPulse] = useState(true);
  
  // 输入方式状态
  const [activeInputMethod, setActiveInputMethod] = useState<string>('text');
  const [isRecording, setIsRecording] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  
  // 专业模式参数
  const [showAdvancedParams, setShowAdvancedParams] = useState({
    music: false,
    vocal: false,
    lyrics: false,
    advanced: false
  });
  
  const [musicParams, setMusicParams] = useState({
    type: 'song',
    styles: ['流行'],
    mood: 'happy',
    duration: 150,
    bpm: 120
  });
  
  // 歌词相关状态
  const [lyricsConfig, setLyricsConfig] = useState({
    content: '',
    wordCount: 0
  });
  
  // 长按相关
  const [isLongPressing, setIsLongPressing] = useState(false);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Refs
  const animationRef = useRef<number | undefined>(undefined);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);

  // ========== 生命周期 ==========
  useEffect(() => {
    // 初始化AI语话
    updateAiTalk();
    
    // 初始化粒子系统
    initParticles();
    
    // 时间更新
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    
    // AI语话轮播
    const talkTimer = setInterval(() => {
      updateAiTalk();
    }, 15000); // 每15秒更换一次语话
    
    return () => {
      clearInterval(timer);
      clearInterval(talkTimer);
    };
  }, []);

  // 打字机效果
  useEffect(() => {
    if (currentAiTalk) {
      setAiTalkMessage('');
      let index = 0;
      const text = currentAiTalk.message;
      
      const typeChar = () => {
        if (index < text.length) {
          setAiTalkMessage(text.substring(0, index + 1));
          index++;
          typingTimer.current = setTimeout(typeChar, 50);
        }
      };
      
      typingTimer.current = setTimeout(typeChar, 300);
      
      return () => {
        if (typingTimer.current) {
          clearTimeout(typingTimer.current);
        }
      };
    }
  }, [currentAiTalk]);

  // 更新AI语话
  const updateAiTalk = useCallback(() => {
    const hour = new Date().getHours();
    let talk: AITalk;
    
    if (hour >= 6 && hour < 12) {
      talk = {
        message: "早安！今天想创作什么样的音乐唤醒耳朵？",
        quickReplies: [
          { text: "元气满满", action: "morning_energy" },
          { text: "轻松惬意", action: "morning_calm" }
        ],
        mood: 'happy'
      };
    } else if (hour >= 22 || hour < 6) {
      talk = aiTalkLibrary[2]; // 深夜助眠
    } else if (hour >= 12 && hour < 14) {
      talk = {
        message: "午休时间，来点舒缓的背景音乐？",
        quickReplies: [
          { text: "来一首", action: "play_relax" },
          { text: "我要嗨歌", action: "play_energetic" }
        ],
        mood: 'calm'
      };
    } else {
      // 随机选择一个语话
      talk = aiTalkLibrary[Math.floor(Math.random() * aiTalkLibrary.length)];
    }
    
    setCurrentAiTalk(talk);
    if (talk.mood) {
      setCurrentMood(talk.mood);
    }
  }, []);

  // ========== 粒子系统 ==========
  const initParticles = useCallback(() => {
    const newParticles: Particle[] = [];
    const colors = moodColors[currentMood].particles;
    const particleTypes: Particle['type'][] = ['note', 'star', 'heart', 'wave'];
    
    for (let i = 0; i < 15; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 430,
        y: Math.random() * 932,
        vx: (Math.random() - 0.5) * 0.3,
        vy: (Math.random() - 0.5) * 0.3,
        size: Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.2 + 0.05,
        color: colors[Math.floor(Math.random() * colors.length)],
        type: particleTypes[Math.floor(Math.random() * particleTypes.length)],
        rotation: Math.random() * 360,
        rotationSpeed: (Math.random() - 0.5) * 2
      });
    }
    setParticles(newParticles);
  }, [currentMood]);

  useEffect(() => {
    initParticles();
  }, [currentMood, initParticles]);

  // 粒子动画循环
  useEffect(() => {
    const animate = () => {
      setParticles(prevParticles => 
        prevParticles.map(particle => {
          let newX = particle.x + particle.vx;
          let newY = particle.y + particle.vy;
          let newVx = particle.vx;
          let newVy = particle.vy;
          
          if (newX <= 0 || newX >= 430) newVx = -newVx;
          if (newY <= 0 || newY >= 932) newVy = -newVy;
          
          return {
            ...particle,
            x: newX,
            y: newY,
            vx: newVx,
            vy: newVy,
            rotation: particle.rotation + particle.rotationSpeed,
            opacity: particle.opacity + Math.sin(Date.now() * 0.001 + particle.id) * 0.01
          };
        })
      );
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  // ========== 场景模板处理 ==========
  const handleTemplateSelect = useCallback((template: SceneTemplate) => {
    setSelectedTemplate(template);
    setInputText(template.prompt);
    setCurrentMood(template.mood);
    setMusicParams(prev => ({
      ...prev,
      styles: template.style,
      mood: template.mood
    }));
    
    // 更新AI话语
    setCurrentAiTalk({
      message: `已选择${template.name}模板，填写关键信息即可生成！`,
      mood: template.mood as any
    });
  }, []);

  // ========== 音乐生成处理 ==========
  const handleGenerate = useCallback(async () => {
    if (!inputText.trim() && !selectedTemplate) {
      // 提示用户输入
      setCurrentAiTalk({
        message: "告诉我你的灵感，或选择一个场景模板开始创作！",
        mood: 'happy'
      });
      return;
    }

    setGenerationState({
      isGenerating: true,
      progress: 0,
      message: '正在理解你的创意...'
    });

    // 模拟生成过程
    const messages = [
      { progress: 20, text: '分析情感色彩...' },
      { progress: 40, text: '编织美妙旋律...' },
      { progress: 60, text: '添加和声配器...' },
      { progress: 80, text: '优化音质效果...' },
      { progress: 100, text: '作品即将呈现...' }
    ];

    for (const msg of messages) {
      await new Promise(resolve => setTimeout(resolve, 600));
      setGenerationState(prev => ({
        ...prev,
        progress: msg.progress,
        message: msg.text
      }));
    }

    // 生成完成
    const generatedMusic: GeneratedMusic = {
      id: `music-${Date.now()}`,
      title: selectedTemplate ? `我的${selectedTemplate.name}` : '无题',
      duration: `${Math.floor(musicParams.duration / 60)}:${(musicParams.duration % 60).toString().padStart(2, '0')}`,
      waveform: Array.from({ length: 50 }, () => Math.random() * 100),
      coverGradient: moodColors[currentMood].primary,
      mood: currentMood,
      style: musicParams.styles,
      bpm: musicParams.bpm,
      lyrics: inputText.split('\n').filter(line => line.trim())
    };

    setGenerationState({
      isGenerating: false,
      progress: 100,
      message: '创作完成！',
      result: generatedMusic
    });

    // 更新AI话语为新的提示
    setCurrentAiTalk({
      message: "结果出来啦，听听看，如果不满意可以修改刚才的灵感参数继续生成哦。",
      mood: 'happy'
    });
  }, [inputText, selectedTemplate, musicParams, currentMood]);

  // 添加删除音乐的处理函数
  const handleDeleteMusic = useCallback(() => {
    setGenerationState({
      isGenerating: false,
      progress: 0,
      message: ''
    });
    
    setCurrentAiTalk({
      message: "音乐已从音乐库中删除，可以重新创作新的作品！",
      mood: 'calm'
    });
  }, []);

  // ========== 输入方式处理 ==========
  const handleInputMethodChange = useCallback((method: string) => {
    setActiveInputMethod(method);
    
    if (method === 'image' || method === 'video') {
      fileInputRef.current?.click();
    } else if (method === 'voice') {
      setIsRecording(!isRecording);
      if (!isRecording) {
        // 开始录音
        setCurrentAiTalk({
          message: "正在聆听...可以哼唱旋律或描述你的想法",
          mood: 'calm'
        });
      } else {
        // 停止录音
        setCurrentAiTalk({
          message: "录音已保存，正在分析...",
          mood: 'happy'
        });
        // 模拟分析
        setTimeout(() => {
          setInputText('刚才哼唱的旋律很好听，充满希望的感觉...');
        }, 1500);
      }
    }
  }, [isRecording]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      const fileType = file.type.startsWith('image/') ? '图片' : '视频';
      setCurrentAiTalk({
        message: `正在分析${fileType}内容，提取情感和氛围...`,
        mood: 'calm'
      });
      
      // 模拟分析
      setTimeout(() => {
        setInputText(`基于上传的${fileType}：温暖的阳光，轻松的氛围，适合创作治愈系音乐`);
        setCurrentMood('happy');
      }, 2000);
    }
  }, []);

  // ========== AI快捷回复处理 ==========
  const handleQuickReply = useCallback((reply: { text: string; action: string }) => {
    // 根据action执行相应操作
    switch (reply.action) {
      case 'start_create':
        setCreationMode('simple');
        break;
      case 'view_templates':
        // 滚动到模板区域
        break;
      case 'love_song':
        handleTemplateSelect(sceneTemplates[0]);
        break;
      case 'healing':
        setCurrentMood('calm');
        break;
      default:
        break;
    }
    
    // 更新AI语话反馈
    setCurrentAiTalk({
      message: `好的，${reply.text}，让我们开始吧！`,
      mood: 'excited'
    });
  }, [handleTemplateSelect]);

  // ========== AI交互处理 ==========
  const handleAiButtonClick = useCallback(() => {
    if (!isLongPressing) {
      setShowAiChat(true);
      if (aiMessages.length === 0) {
        // 初始欢迎消息
        const welcomeMessage: AIMessage = {
          id: 'welcome',
          type: 'ai',
          content: '嗨！我是小弦，你的AI音乐创作伙伴！需要什么帮助吗？',
          timestamp: new Date(),
          suggestions: ['给我灵感', '帮我写词', '推荐风格', '分析参考歌曲']
        };
        setAiMessages([welcomeMessage]);
      }
    }
  }, [isLongPressing, aiMessages]);

  const handleAiButtonLongPress = useCallback(() => {
    longPressTimer.current = setTimeout(() => {
      setIsLongPressing(true);
      setShowQuickReply(true);
      // 触觉反馈
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, 300);
  }, []);

  const handleAiButtonRelease = useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
    }
    setIsLongPressing(false);
    setTimeout(() => {
      setShowQuickReply(false);
    }, 200);
  }, []);

  const handleAiSubmit = useCallback(() => {
    if (!aiInputText.trim()) return;
    
    // 添加用户消息
    const userMessage: AIMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: aiInputText,
      timestamp: new Date()
    };
    
    setAiMessages(prev => [...prev, userMessage]);
    setAiInputText('');
    
    // 模拟AI回复
    setTimeout(() => {
      let response = '';
      let suggestions: string[] = [];
      
      if (aiInputText.includes('灵感')) {
        response = '试试想象一个场景：夕阳下的海边，微风轻拂，有什么想对TA说的吗？';
        suggestions = ['就用这个', '换一个', '更浪漫些'];
      } else if (aiInputText.includes('写词') || aiInputText.includes('歌词')) {
        response = '我来帮你续写歌词！把你已有的词句告诉我，或者描述想表达的感觉';
        suggestions = ['开始写词', '看看示例'];
      } else if (aiInputText.includes('风格')) {
        response = '根据你的描述，我推荐"流行+R&B"的组合，温柔又不失节奏感';
        suggestions = ['使用推荐', '其他风格'];
      } else {
        response = `好的，我来帮你${aiInputText}`;
        suggestions = ['开始', '了解更多'];
      }
      
      const aiResponse: AIMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: response,
        timestamp: new Date(),
        suggestions
      };
      
      setAiMessages(prev => [...prev, aiResponse]);
      
      // 滚动到底部
      setTimeout(() => {
        if (chatScrollRef.current) {
          chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
        }
      }, 100);
    }, 800);
  }, [aiInputText]);

  const handleQuickAction = useCallback((action: string) => {
    switch (action) {
      case 'inspiration':
        setInputText('今天阳光很好，想创作一首温暖的歌...');
        break;
      case 'lyrics':
        setShowAiChat(true);
        setAiInputText('帮我写一段关于青春的歌词');
        handleAiSubmit();
        break;
      case 'style':
        setCreationMode('professional');
        setShowAdvancedParams({ ...showAdvancedParams, music: true });
        break;
      case 'random':
        // 随机创作
        const randomTemplate = sceneTemplates[Math.floor(Math.random() * sceneTemplates.length)];
        handleTemplateSelect(randomTemplate);
        break;
    }
    setShowQuickReply(false);
  }, [showAdvancedParams, handleAiSubmit, handleTemplateSelect]);

  // ========== 渲染粒子形状 ==========
  const renderParticleShape = (type: Particle['type']) => {
    switch (type) {
      case 'note': return '♪';
      case 'star': return '✦';
      case 'heart': return '♥';
      case 'wave': return '~';
      default: return '•';
    }
  };

  // ========== 专业模式参数组件 ==========
  const ProfessionalParams = () => (
    <div className="space-y-3 ">
      {/* 音乐配置 */}
      <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
        <button
          className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
          onClick={() => setShowAdvancedParams({
            ...showAdvancedParams,
            music: !showAdvancedParams.music
          })}
        >
          <span className="flex items-center gap-2">
            <Music className="w-4 h-4" />
            音乐配置
          </span>
          <ChevronDown className={`w-4 h-4 transition-transform ${
            showAdvancedParams.music ? 'rotate-180' : ''
          }`} />
        </button>
        
        {showAdvancedParams.music && (
          <div className="px-4 pb-4 space-y-4 border-t border-white/10">
            {/* 音乐类型 */}
            <div className="pt-3">
              <label className="text-white/60 text-xs mb-2 block">音乐类型</label>
              <div className="flex gap-2">
                <button
                  className={`flex-1 py-2 rounded-xl text-sm transition-all ${
                    musicParams.type === 'song'
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                      : 'bg-white/10 text-white/60 hover:bg-white/20'
                  }`}
                  onClick={() => setMusicParams({ ...musicParams, type: 'song' })}
                >
                  🎵 歌曲
                </button>
                <button
                  className={`flex-1 py-2 rounded-xl text-sm transition-all ${
                    musicParams.type === 'instrumental'
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                      : 'bg-white/10 text-white/60 hover:bg-white/20'
                  }`}
                  onClick={() => setMusicParams({ ...musicParams, type: 'instrumental' })}
                >
                  🎹 纯音乐
                </button>
              </div>
            </div>

            {/* 音乐风格 */}
            <div>
              <label className="text-white/60 text-xs mb-2 block">音乐风格（可多选）</label>
              <div className="flex flex-wrap gap-2">
                {['流行', '摇滚', '民谣', '电子', '爵士', 'R&B', '古风', '说唱'].map(style => (
                  <button
                    key={style}
                    className={`px-3 py-1.5 rounded-full text-sm transition-all ${
                      musicParams.styles.includes(style)
                        ? 'bg-white/20 text-white border border-white/30'
                        : 'bg-white/10 text-white/60 hover:bg-white/15'
                    }`}
                    onClick={() => {
                      if (musicParams.styles.includes(style)) {
                        setMusicParams({
                          ...musicParams,
                          styles: musicParams.styles.filter(s => s !== style)
                        });
                      } else {
                        setMusicParams({
                          ...musicParams,
                          styles: [...musicParams.styles, style]
                        });
                      }
                    }}
                  >
                    {style}
                  </button>
                ))}
              </div>
            </div>

            {/* 时长调节 */}
            <div>
              <label className="text-white/60 text-xs mb-2 block">
                歌曲时长：{Math.floor(musicParams.duration / 60)}:{(musicParams.duration % 60).toString().padStart(2, '0')}
              </label>
              <div className="relative">
                <input
                  type="range"
                  min="30"
                  max="300"
                  value={musicParams.duration}
                  onChange={(e) => setMusicParams({ ...musicParams, duration: parseInt(e.target.value) })}
                  className="w-full h-2 bg-white/10 rounded-full appearance-none cursor-pointer slider"
                />
                <div 
                  className="absolute top-0 left-0 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full pointer-events-none"
                  style={{ width: `${((musicParams.duration - 30) / 270) * 100}%` }}
                />
              </div>
            </div>

            {/* BPM调节 */}
            <div>
              <label className="text-white/60 text-xs mb-2 block">
                节奏速度：{musicParams.bpm} BPM
              </label>
              <div className="relative">
                <input
                  type="range"
                  min="60"
                  max="180"
                  value={musicParams.bpm}
                  onChange={(e) => setMusicParams({ ...musicParams, bpm: parseInt(e.target.value) })}
                  className="w-full h-2 bg-white/10 rounded-full appearance-none cursor-pointer slider"
                />
                <div 
                  className="absolute top-0 left-0 h-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full pointer-events-none"
                  style={{ width: `${((musicParams.bpm - 60) / 120) * 100}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-white/40 mt-1">
                <span>慢板</span>
                <span>中速</span>
                <span>快板</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 演唱设置 */}
      {musicParams.type === 'song' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
          <button
            className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
            onClick={() => setShowAdvancedParams({
              ...showAdvancedParams,
              vocal: !showAdvancedParams.vocal
            })}
          >
            <span className="flex items-center gap-2">
              <Mic className="w-4 h-4" />
              演唱设置
            </span>
            <ChevronDown className={`w-4 h-4 transition-transform ${
              showAdvancedParams.vocal ? 'rotate-180' : ''
            }`} />
          </button>
          
          {showAdvancedParams.vocal && (
            <div className="px-4 pb-4 space-y-4 border-t border-white/10">
              <div className="pt-3">
                <label className="text-white/60 text-xs mb-2 block">人声选择</label>
                <div className="grid grid-cols-4 gap-2">
                  {[
                    { id: 'male', label: '男声', icon: '🎤' },
                    { id: 'female', label: '女声', icon: '🎵' },
                    { id: 'child', label: '童声', icon: '🎶' },
                    { id: 'clone', label: '克隆', icon: '🎙️' }
                  ].map(voice => (
                    <button
                      key={voice.id}
                      className="py-2 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm transition-all flex flex-col items-center gap-1"
                    >
                      <span className="text-lg">{voice.icon}</span>
                      <span className="text-xs">{voice.label}</span>
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-white/60 text-xs mb-2 block">声音特征</label>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <span className="text-xs text-white/40 w-12">温柔</span>
                    <input type="range" className="flex-1 h-1 bg-white/10 rounded-full appearance-none" />
                    <span className="text-xs text-white/40 w-12 text-right">有力</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-xs text-white/40 w-12">低沉</span>
                    <input type="range" className="flex-1 h-1 bg-white/10 rounded-full appearance-none" />
                    <span className="text-xs text-white/40 w-12 text-right">明亮</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 自定义歌词 */}
      {musicParams.type === 'song' && (
        <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
          <button
            className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
            onClick={() => setShowAdvancedParams({
              ...showAdvancedParams,
              lyrics: !showAdvancedParams.lyrics
            })}
          >
            <span className="flex items-center gap-2">
              <PenTool className="w-4 h-4" />
              自定义歌词
            </span>
            <ChevronDown className={`w-4 h-4 transition-transform ${
              showAdvancedParams.lyrics ? 'rotate-180' : ''
            }`} />
          </button>
          
          {showAdvancedParams.lyrics && (
            <div className="px-4 pb-4 space-y-4 border-t border-white/10">
              <div className="pt-3">
                <div className="bg-white/5 rounded-xl p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/60 text-xs">歌词内容</span>
                    <span className="text-white/40 text-xs">{lyricsConfig.wordCount || 0}/1000字</span>
                  </div>
                  <textarea
                    placeholder="在这里输入你的歌词...\n\n可以是诗歌、散文\n或者任何你想表达的形式"
                    value={lyricsConfig.content || ''}
                    onChange={(e) => {
                      const content = e.target.value;
                      const wordCount = content.replace(/\s/g, '').length;
                      setLyricsConfig({ ...lyricsConfig, content, wordCount });
                    }}
                    className="w-full h-40 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-purple-400 transition-colors resize-none"
                    maxLength={1000}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 高级参数 */}
      <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden">
        <button
          className="w-full px-4 py-3 flex items-center justify-between text-white hover:bg-white/5 transition-all"
          onClick={() => setShowAdvancedParams({
            ...showAdvancedParams,
            advanced: !showAdvancedParams.advanced
          })}
        >
          <span className="flex items-center gap-2">
            <SlidersHorizontal className="w-4 h-4" />
            高级参数
          </span>
          <ChevronDown className={`w-4 h-4 transition-transform ${
            showAdvancedParams.advanced ? 'rotate-180' : ''
          }`} />
        </button>
        
        {showAdvancedParams.advanced && (
          <div className="px-4 pb-4 space-y-4 border-t border-white/10">
            <div className="pt-3">
              <label className="text-white/60 text-xs mb-2 block">主要乐器（最多3个）</label>
              <div className="flex flex-wrap gap-2">
                {[
                  { id: 'piano', label: '钢琴', icon: Piano },
                  { id: 'guitar', label: '吉他', icon: Guitar },
                  { id: 'drum', label: '鼓', icon: Drum },
                  { id: 'violin', label: '小提琴', icon: Music },
                  { id: 'bass', label: '贝斯', icon: Radio },
                  { id: 'synth', label: '合成器', icon: Settings }
                ].map(instrument => (
                  <button
                    key={instrument.id}
                    className="px-3 py-1.5 bg-white/10 hover:bg-white/20 rounded-full text-white/60 text-sm transition-all flex items-center gap-1"
                  >
                    <instrument.icon className="w-3 h-3" />
                    {instrument.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // ==================== 主渲染 ====================
  return (
    <div className="relative mx-auto" style={{ maxWidth: '430px', height: '932px' }}>
      {/* 手机框架 */}
      <div className="relative w-full h-full bg-black rounded-[3rem] p-[3px] shadow-2xl">
        <div className="relative w-full h-full bg-gray-900 rounded-[2.8rem] overflow-hidden flex flex-col">
          
          {/* 状态栏 */}
          <div className="absolute top-0 left-0 right-0 z-50 px-8 pt-3 pb-1">
            <div className="flex justify-between items-center text-white text-sm">
              <div className="flex items-center gap-1">
                <span className="font-medium">
                  {currentTime.getHours().toString().padStart(2, '0')}:
                  {currentTime.getMinutes().toString().padStart(2, '0')}
                </span>
              </div>
              <div className="absolute left-1/2 transform -translate-x-1/2 w-24 h-7 bg-black rounded-full" />
              <div className="flex items-center gap-1">
                <Bell className="w-3.5 h-3.5 text-white/70" />
                <div className="w-6 h-3 border border-white/50 rounded-sm">
                  <div className="h-full bg-white rounded-sm" style={{ width: '85%' }} />
                </div>
              </div>
            </div>
          </div>

          {/* 动态背景层 */}
          <div className={`absolute inset-0 bg-gradient-to-br ${moodColors[currentMood].primary} opacity-10 transition-all duration-1000`} />
          
          {/* 粒子系统 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {particles.map((particle) => (
              <div
                key={particle.id}
                className="absolute animate-float-particle"
                style={{
                  left: `${particle.x}px`,
                  top: `${particle.y}px`,
                  transform: `translate(-50%, -50%) rotate(${particle.rotation}deg)`,
                  fontSize: `${particle.size * 5}px`,
                  color: particle.color,
                  opacity: particle.opacity,
                  filter: 'blur(0.5px)',
                  textShadow: `0 0 ${particle.size * 3}px ${particle.color}`
                }}
              >
                {renderParticleShape(particle.type)}
              </div>
            ))}
          </div>

          {/* 主容器 */}
          <div className="flex flex-col h-full pt-14 relative">
            {/* AI语话区 - 与首页保持一致 */}
            <div className="px-6 pb-3 z-20">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
              {/* AI头像和状态 */}
              <div className="flex items-start gap-3 mb-3">
                <div className={`w-10 h-10 rounded-full bg-gradient-to-br ${moodColors[currentMood].primary} flex items-center justify-center flex-shrink-0`}>
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-white font-medium text-sm">小弦</span>
                    <span className="text-xs text-white/40">AI音乐创作伙伴</span>
                  </div>
                  <p className="text-white/80 text-sm leading-relaxed">
                    {aiTalkMessage}
                    {aiTalkMessage.length < currentAiTalk.message.length && (
                      <span className="inline-block w-1 h-4 ml-1 bg-white/60 animate-blink" />
                    )}
                  </p>
                </div>
              </div>
              
              {/* 快捷回复按钮 */}
              {currentAiTalk.quickReplies && aiTalkMessage === currentAiTalk.message && (
                <div className="flex gap-2 mt-3 animate-fade-in">
                  {currentAiTalk.quickReplies.map((reply, index) => (
                    <button
                      key={index}
                      className="px-4 py-1.5 bg-white/10 hover:bg-white/20 rounded-full text-white text-sm transition-all active:scale-95"
                      onClick={() => handleQuickReply(reply)}
                    >
                      {reply.text}
                    </button>
                  ))}
                </div>
              )}
              
              {/* 提示文字 */}
              <div className="mt-3 flex items-center gap-1 text-white/40 text-xs">
                <span>💡</span>
                <span>长按右下角音律珠可快速回复哦</span>
              </div>
            </div>
            </div>

            {/* 模式切换 */}
            <div className="px-6 pb-3 z-20">
            <div className="bg-white/5 backdrop-blur-lg rounded-xl p-1 flex">
              <button
                className={`flex-1 py-2 rounded-lg text-sm font-medium transition-all ${
                  creationMode === 'simple'
                    ? 'bg-white/20 text-white'
                    : 'text-white/60 hover:text-white'
                }`}
                onClick={() => setCreationMode('simple')}
              >
                极简模式
              </button>
              <button
                className={`flex-1 py-2 rounded-lg text-sm font-medium transition-all ${
                  creationMode === 'professional'
                    ? 'bg-white/20 text-white'
                    : 'text-white/60 hover:text-white'
                }`}
                onClick={() => setCreationMode('professional')}
              >
                专业模式
              </button>
            </div>
            </div>

            {/* 主内容区域 */}
            <div className="flex-1 overflow-y-auto px-6 pb-32 no-scrollbar relative min-h-0">
            {/* 输入区域 */}
            <div className="mb-4">
              <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-4 border border-white/10">
                <div className="mb-3">
                  <textarea
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="告诉我你的灵感..."
                    className="w-full min-h-[100px] bg-transparent text-white placeholder-white/40 outline-none resize-none text-sm leading-relaxed"
                  />
                </div>
                
                {/* 输入方式按钮 */}
                <div className="flex gap-3">
                  <button
                    className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all ${
                      activeInputMethod === 'image' ? 'bg-white/20' : 'bg-white/10 hover:bg-white/15'
                    }`}
                    onClick={() => handleInputMethodChange('image')}
                  >
                    <Camera className="w-5 h-5 text-white/80" />
                  </button>
                  <button
                    className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all ${
                      isRecording ? 'bg-red-500/20 animate-pulse' : 'bg-white/10 hover:bg-white/15'
                    }`}
                    onClick={() => handleInputMethodChange('voice')}
                  >
                    {isRecording ? (
                      <MicOff className="w-5 h-5 text-red-400" />
                    ) : (
                      <Mic className="w-5 h-5 text-white/80" />
                    )}
                  </button>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*,video/*"
                    className="hidden"
                    onChange={handleFileUpload}
                  />
                </div>

                {/* 上传文件显示 */}
                {uploadedFile && (
                  <div className="mt-3 p-2 bg-white/5 rounded-lg flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Image className="w-4 h-4 text-white/60" />
                      <span className="text-sm text-white/80">{uploadedFile.name}</span>
                    </div>
                    <button
                      className="text-white/40 hover:text-white/60"
                      onClick={() => setUploadedFile(null)}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* 热门场景模板 */}
            {creationMode === 'simple' && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h2 className="text-white/80 text-sm font-medium flex items-center gap-2">
                    🔥 热门场景
                  </h2>
                  <button className="text-white/40 text-xs hover:text-white/60 transition-all">
                    查看全部 →
                  </button>
                </div>
                <div className="flex gap-3 overflow-x-auto no-scrollbar pb-2">
                  {sceneTemplates.map((template) => (
                    <button
                      key={template.id}
                      className={`flex-shrink-0 transition-all ${
                        selectedTemplate?.id === template.id
                          ? 'scale-105'
                          : 'hover:scale-105'
                      }`}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <div className={`relative bg-gradient-to-br ${template.color} rounded-2xl p-4 w-24 h-24 overflow-hidden ${
                        selectedTemplate?.id === template.id
                          ? 'ring-2 ring-white/50 ring-offset-2 ring-offset-transparent'
                          : ''
                      }`}>
                        <div className="absolute inset-0 bg-black/20" />
                        <div className="relative h-full flex flex-col items-center justify-center">
                          <span className="text-2xl mb-1">{template.emoji}</span>
                          <span className="text-white text-xs font-medium">{template.name}</span>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* 专业模式参数 */}
            {creationMode === 'professional' && <ProfessionalParams />}

              {/* 生成结果预览 */}
              {generationState.result && (
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-4 animate-scale-up">
                  {/* 标题区 */}
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-white font-medium text-lg">{generationState.result.title}</h3>
                      <p className="text-white/60 text-sm">@{currentTime.toLocaleDateString()}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1 px-2 py-1 bg-green-500/20 rounded-full">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span className="text-green-400 text-xs">已存入音乐库中</span>
                      </div>
                      <button 
                        className="w-8 h-8 bg-red-500/20 hover:bg-red-500/30 rounded-full flex items-center justify-center transition-all"
                        onClick={handleDeleteMusic}
                        title="从音乐库中删除"
                      >
                        <X className="w-4 h-4 text-red-400" />
                      </button>
                    </div>
                  </div>

                  {/* 封面和波形 */}
                  <div className={`relative h-48 bg-gradient-to-br ${generationState.result.coverGradient} rounded-xl mb-4 overflow-hidden`}>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Music className="w-20 h-20 text-white/30" />
                    </div>
                    
                    {/* 波形可视化 */}
                    <div className="absolute bottom-0 left-0 right-0 flex items-end justify-around h-24 px-2 overflow-hidden">
                      {generationState.result.waveform.slice(0, 30).map((height, i) => (
                        <div
                          key={i}
                          className="w-1 bg-white/40 rounded-full transition-all duration-300"
                          style={{
                            height: `${height * 0.6}%`,
                            animation: `wave-once 0.5s ease-out ${i * 20}ms forwards`,
                            transformOrigin: 'bottom'
                          }}
                        />
                      ))}
                    </div>
                  </div>

                  {/* 播放控制 */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <button className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg hover:scale-105 transition-all">
                        <Play className="w-5 h-5 text-white ml-0.5" />
                      </button>
                      <div className="flex-1">
                        <div className="h-1 bg-white/10 rounded-full overflow-hidden">
                          <div className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full" style={{ width: '0%' }} />
                        </div>
                        <div className="flex justify-between text-xs text-white/40 mt-1">
                          <span>0:00</span>
                          <span>{generationState.result.duration}</span>
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 - 只保留发布作品 */}
                    <div className="pt-3">
                      <button className="w-full py-2.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white text-sm font-medium transition-all hover:shadow-lg hover:scale-105 flex items-center justify-center gap-2">
                        <Check className="w-4 h-4" />
                        发布作品
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 生成按钮 */}
          <div className="absolute bottom-20 left-0 right-0 px-6 z-30">
            <button
              className={`w-full py-4 rounded-2xl font-medium text-white transition-all transform ${
                generationState.isGenerating
                  ? 'bg-gray-700 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:shadow-xl hover:scale-105 active:scale-95'
              }`}
              onClick={handleGenerate}
              disabled={generationState.isGenerating}
            >
              {generationState.isGenerating ? (
                <div className="flex items-center justify-center gap-3">
                  <div className="flex gap-1">
                    <span className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                    <span className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                    <span className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                  </div>
                  <span>{generationState.message}</span>
                  <span className="text-sm">({generationState.progress}%)</span>
                </div>
              ) : (
                <span className="flex items-center justify-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  生成我的音乐
                </span>
              )}
            </button>

            {/* 生成进度条 */}
            {generationState.isGenerating && (
              <div className="mt-2 h-1 bg-white/10 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full transition-all duration-300"
                  style={{ width: `${generationState.progress}%` }}
                />
              </div>
            )}
          </div>

          {/* AI聊天覆盖层 */}
          {showAiChat && (
            <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-40 animate-fade-in">
              <div className="flex flex-col h-full pt-16">
                {/* 对话内容 */}
                <div 
                  ref={chatScrollRef}
                  className="flex-1 overflow-y-auto px-6 py-4 space-y-4"
                >

                  {aiMessages.map(message => (
                    <div 
                      key={message.id}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.type === 'ai' && (
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center mr-2 flex-shrink-0">
                          <Sparkles className="w-4 h-4 text-white" />
                        </div>
                      )}
                      
                      <div className={`max-w-[75%]`}>
                        <div className={`px-4 py-2 rounded-2xl ${
                          message.type === 'user' 
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                            : 'bg-white/10 text-white backdrop-blur'
                        }`}>
                          <p className="text-sm">{message.content}</p>
                        </div>
                        {message.suggestions && (
                          <div className="mt-2 flex gap-2 flex-wrap">
                            {message.suggestions.map((suggestion, i) => (
                              <button 
                                key={i}
                                className="px-3 py-1 bg-white/10 backdrop-blur rounded-full text-xs text-white/80 hover:bg-white/20 transition-all"
                                onClick={() => {
                                  setAiInputText(suggestion);
                                  handleAiSubmit();
                                }}
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* 输入区域 */}
                <div className="border-t border-white/10 px-4 py-3 bg-black/30 backdrop-blur">
                  <div className="flex items-center gap-3">
                    <button 
                      className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center active:scale-95 hover:bg-white/20 transition-all"
                      onClick={() => setShowAiChat(false)}
                    >
                      <X className="w-5 h-5 text-white" />
                    </button>
                    <div className="flex-1 bg-white/10 backdrop-blur rounded-full px-4 py-2 flex items-center gap-2">
                      <input
                        ref={inputRef}
                        type="text"
                        value={aiInputText}
                        onChange={(e) => setAiInputText(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleAiSubmit()}
                        placeholder="说点什么..."
                        className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                      />
                      <button 
                        className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center active:scale-95"
                        onClick={handleAiSubmit}
                      >
                        <Send className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 快捷回复悬浮层 */}
          {showQuickReply && (
            <div className="absolute bottom-24 right-4 z-50 animate-scale-up">
              <div className="bg-black/80 backdrop-blur-lg rounded-2xl p-3 border border-white/20 min-w-[160px]">
                <div className="space-y-2">

                  <button
                    className="w-full px-4 py-2 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm text-left transition-all flex items-center gap-2"
                    onClick={() => handleQuickAction('lyrics')}
                  >
                    <FileText className="w-4 h-4" />
                    帮我写词
                  </button>

                  <button
                    className="w-full px-4 py-2 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm text-left transition-all flex items-center gap-2"
                    onClick={() => handleQuickAction('random')}
                  >
                    <Shuffle className="w-4 h-4" />
                    随机创作
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 底部导航栏 */}
          <div className="absolute bottom-0 left-0 right-0 bg-black/30 backdrop-blur-xl border-t border-white/10">
            <div className="flex items-center justify-between px-6 py-3">
              {/* 首页 */}
              <button className="flex flex-col items-center gap-1 py-1 text-white/50">
                <Home className="w-5 h-5" />
                <span className="text-xs">首页</span>
              </button>

              {/* 创作 - 当前页面 */}
              <div className="flex flex-col items-center gap-1 py-1 text-white">
                <PenTool className="w-5 h-5" />
                <span className="text-xs">创作</span>
              </div>

              {/* 我的 */}
              <div className="flex flex-col items-center gap-1 py-1 text-white/50">
                <User className="w-5 h-5" />
                <span className="text-xs">我的</span>
              </div>

              {/* 音律珠 - AI交互入口 */}
              <div className="relative">
                <div 
                  className={`relative transition-all duration-300 ${
                    isLongPressing ? 'scale-110' : ''
                  }`}
                  onTouchStart={handleAiButtonLongPress}
                  onTouchEnd={handleAiButtonRelease}
                  onMouseDown={handleAiButtonLongPress}
                  onMouseUp={handleAiButtonRelease}
                  onMouseLeave={handleAiButtonRelease}
                  onClick={handleAiButtonClick}
                >
                  {/* 呼吸光环 */}
                  {aiButtonPulse && (
                    <div className="absolute -inset-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 animate-pulse-slow" />
                  )}
                  
                  {/* 音律珠主体 */}
                  <div className={`relative w-14 h-14 rounded-full overflow-hidden cursor-pointer ${
                    isLongPressing 
                      ? 'bg-gradient-to-br from-green-500 to-emerald-500' 
                      : 'bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500'
                  } shadow-2xl transform transition-all hover:scale-105`}>
                    {/* 内部流动效果 */}
                    <div className="absolute inset-0">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-full h-full animate-flow"
                          style={{
                            background: `radial-gradient(circle at ${30 + i * 20}% ${30 + i * 20}%, rgba(255,255,255,0.3) 0%, transparent 50%)`,
                            animationDelay: `${i * 0.5}s`
                          }}
                        />
                      ))}
                    </div>
                    
                    {/* 中心符号 */}
                    <div className="relative w-full h-full flex items-center justify-center">
                      <div className="text-white text-2xl font-bold">✧</div>
                      {/* 状态指示点 */}
                      <div className="absolute bottom-1.5 right-1.5 w-2 h-2 rounded-full bg-white animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Home Indicator */}
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/30 rounded-full" />
        </div>
      </div>
    </div>
  );
};

// ==================== 样式注入 ====================
const style = document.createElement('style');
style.textContent = `
  @keyframes float-particle {
    0%, 100% { 
      transform: translate(-50%, -50%) translateY(0px) rotate(0deg);
    }
    50% { 
      transform: translate(-50%, -50%) translateY(-10px) rotate(180deg);
    }
  }
  
  @keyframes flow {
    0% { transform: translateY(100%) rotate(0deg); }
    100% { transform: translateY(-100%) rotate(360deg); }
  }
  
  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes scale-up {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  
  @keyframes wave {
    0%, 100% { height: 20%; }
    50% { height: 60%; }
  }
  
  @keyframes wave-once {
  0% { 
    transform: scaleY(0);
    opacity: 0;
  }
  100% { 
    transform: scaleY(1);
    opacity: 1;
  }
}
  
  @keyframes pulse-slow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
  }
  
  @keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-4px); }
  }
  
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
  
  .animate-float-particle {
    animation: float-particle 8s ease-in-out infinite;
  }
  
  .animate-flow {
    animation: flow 6s linear infinite;
  }
  
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }
  
  .animate-scale-up {
    animation: scale-up 0.3s ease-out forwards;
  }
  
  .animate-wave {
    animation: wave 1.5s ease-in-out infinite;
    will-change: height; 
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  .animate-bounce {
    animation: bounce 0.6s ease-in-out infinite;
  }
  
  .animate-blink {
    animation: blink 1s infinite;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  /* Custom range slider styles */
  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    z-index: 1;
  }
  
  .slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    position: relative;
    z-index: 1;
  }
  
  /* Animation for generation */
  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
  
  .shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
`;
document.head.appendChild(style);

export default MusicCreationPage;