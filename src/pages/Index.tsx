import React from 'react';
import { Link } from 'react-router-dom';
import { Music, Home, FileText, Brain, Upload, Map } from 'lucide-react';

const Index: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState<'app' | 'project'>('app');
  const appNavigationItems = [
    {
      title: '音乐首页',
      description: '浏览音乐歌单和推荐内容',
      path: '/music-home',
      icon: Music,
      color: 'bg-gradient-to-r from-purple-500 to-pink-500'
    },
    {
      title: 'AI音乐创建',
      description: '使用AI技术创作独特的音乐作品',
      path: '/ai-music-creation',
      icon: Brain,
      color: 'bg-gradient-to-r from-blue-500 to-purple-500'
    },
    {
      title: 'AI音乐鉴赏',
      description: '让AI为您解读音乐的深层含义',
      path: '/ai-music-appreciation',
      icon: Brain,
      color: 'bg-gradient-to-r from-violet-500 to-purple-500'
    },
    {
      title: '发布作品',
      description: '发布和分享您的音乐作品',
      path: '/publish-work',
      icon: Upload,
      color: 'bg-gradient-to-r from-green-500 to-teal-500'
    }
  ];

  const projectNavigationItems = [
    {
      title: 'PRD',
      description: '产品需求文档和项目规划',
      path: '/prd',
      icon: FileText,
      color: 'bg-gradient-to-r from-indigo-500 to-purple-500'
    },
    {
      title: '调研资料',
      description: '市场调研与用户研究',
      path: '/research',
      icon: FileText,
      color: 'bg-gradient-to-r from-green-500 to-blue-500'
    },
    {
      title: '模板页面',
      description: 'iPhone 16 Pro Max 模板组件',
      path: '/template',
      icon: FileText,
      color: 'bg-gradient-to-r from-cyan-500 to-blue-500'
    },
    {
      title: 'APP页面地图',
      description: '查看应用的页面关系图',
      path: '/site-map',
      icon: Map,
      color: 'bg-gradient-to-r from-yellow-500 to-orange-500'
    },
    {
      title: '首页设计说明',
      description: '首页界面设计规范和说明',
      path: '/home-design',
      icon: FileText,
      color: 'bg-gradient-to-r from-orange-500 to-red-500'
    },
    {
      title: 'AI音乐创作页面设计说明',
      description: 'AI音乐创作功能设计和实现方案',
      path: '/music-creation-design',
      icon: FileText,
      color: 'bg-gradient-to-r from-purple-500 to-pink-500'
    },
    {
      title: '发布页面设计说明',
      description: '发布作品页面的设计规范和实现方案',
      path: '/publish-page-design',
      icon: FileText,
      color: 'bg-gradient-to-r from-emerald-500 to-green-500'
    },
    {
      title: 'AI音乐鉴赏设计说明',
      description: 'AI音乐鉴赏功能设计书',
      path: '/ai-music-appreciation-design',
      icon: FileText,
      color: 'bg-gradient-to-r from-rose-500 to-pink-500'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <Home className="w-12 h-12 text-slate-700 mr-4" />
            <h1 className="text-4xl font-bold text-slate-800">心弦 MoodBeat-AI音乐社区</h1>
          </div>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto">
          让每个故事都能成为一首歌
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-md">
            <button
              onClick={() => setActiveTab('app')}
              className={`px-6 py-2 rounded-md transition-all duration-200 ${
                activeTab === 'app'
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              App 页面
            </button>
            <button
              onClick={() => setActiveTab('project')}
              className={`px-6 py-2 rounded-md transition-all duration-200 ${
                activeTab === 'project'
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              项目资料
            </button>
          </div>
        </div>

        {/* Navigation Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {(activeTab === 'app' ? appNavigationItems : projectNavigationItems).map((item, index) => {
            const IconComponent = item.icon;
            return (
              <Link
                key={index}
                to={item.path}
                className="group relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className={`${item.color} h-32 relative`}>
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />
                  <div className="absolute bottom-4 left-6">
                    <IconComponent className="w-10 h-10 text-white" />
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-slate-800 mb-2 group-hover:text-slate-900 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {item.description}
                  </p>
                </div>
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            );
          })}
        </div>

        {/* Footer */}
        <div className="text-center mt-16">
          <p className="text-slate-500 text-sm">
            © 2024 Music App Platform. 为您提供最佳的用户体验
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;