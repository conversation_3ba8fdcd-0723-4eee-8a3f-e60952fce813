# 心弦 MoodBeat - 首页设计方案 V5.0

## 🎯 设计理念：隐形AI，极致体验

**核心原则：音乐为主，AI为辅，恰到好处，绝不打扰**

- 80%用户只想听歌，20%会创作
- AI小弦是隐形助手，而非主角
- 在用户需要时精准出现，平时保持安静
- 每个交互都能快速达成目标

## 📱 三层智能架构

### 整体布局
```
┌─────────────────────────────────┐
│  状态栏 (系统时间/电量/信号)     │
├─────────────────────────────────┤
│  🎵 心弦 MoodBeat                │ ← 品牌区
├─────────────────────────────────┤
│  ≈≈≈ AI智能丝带 ≈≈≈            │ ← AI播报区(28px)
│  💬 小明评论了你的《雨天》...    │
├─────────────────────────────────┤
│                                 │
│      【音乐瀑布流】             │ ← 主内容区
│     ┌──────────────┐            │
│     │ 正在播放卡片 │            │
│     │              │            │
│     │   [内联建议] │            │
│     └──────────────┘            │
│           ↓                     │
│     ┌──────────────┐            │
│     │  下一首预览  │            │
│     └──────────────┘            │
│                                 │
├─────────────────────────────────┤
│ [首页] [发现] [创作] [我的]  ◉  │ ← 底部导航+AI入口
└─────────────────────────────────┘
```

## 🎭 Layer 1: AI智能丝带（无打扰播报）
### 设计理念
**AI音乐伴侣**：一起听歌/创作的好伙伴
- **APP各功能引导**：引导用户发现APP的各功能模块，如发现新音乐、创作音乐、发现用户等。
- **音乐推荐**：根据用户的 listening history、social network、music tastes 等信息，推荐用户可能喜欢的音乐。
- **创作帮助**：在写歌/写评论等需要进行创作的场景下，提供创作灵感、创作建议、创作工具等。
- **社交互动**：及时通知用户好友的动态、评论、点赞等互动，保持用户与好友的互动。
- 

### 设计规格
- **高度**: 28-32px，紧贴导航栏下方
- **背景**: 半透明毛玻璃效果，不影响下方内容
- **动画**: 从上方滑入，停留5-8秒后自动收起

### 播报内容优先级
```
优先级1 - 社交互动（必须显示）
├── "小明评论了你的《雨天》"
├── "你的作品获得100个赞！"
└── "收到3条私信"

优先级2 - 个性化推荐
├── "今日推荐：适合工作的轻音乐"
├── "你可能喜欢：最新民谣精选"
└── "创作灵感：今天是下雨天，要不要写首歌？"

优先级3 - 社区热点
├── "🔥 社区热门：告白神曲TOP3"
├── "💫 今日话题：用音乐记录2024"
└── "🎵 新晋创作者：@音乐诗人 值得关注"
```

### 交互设计
```
丝带展开状态：
┌─────────────────────────────────────┐
│ 😊 小明评论了你的《雨天》："太有感觉了！" │
│ [查看] [回复] [稍后]           [收起]│
└─────────────────────────────────────┘

点击行为：
- 查看 → 跳转到评论详情
- 回复 → 底部弹出快速回复框
- 稍后 → 丝带收起，加入通知中心
- 收起 → 立即收起丝带
```

### 智能规则
- **频率控制**: 每30秒最多显示一条
- **打扰控制**: 用户操作时（滑动/输入）不显示
- **智能去重**: 相似内容30分钟内不重复
- **静音模式**: 深夜自动进入静音，仅显示重要消息

## 🎵 Layer 2: 音乐瀑布流（核心体验区）

### 播放卡片设计（占70%屏幕）
```
┌──────────────────────────────┐
│   ♪ ♫ ♪ 音波可视化背景      │
│                              │
│    [专辑封面/MV/动态效果]    │
│                              │
│  《失恋第七天》               │
│   @小明的音乐日记            │
│                              │
│  ━━━━━●━━━━━ 1:23/3:45      │
│                              │
│  ❤️ 12k  💬 892  🔄 分享     │
├──────────────────────────────┤
│  📝 查看创作故事             │ ← 故事入口
└──────────────────────────────┘
```

### 内联AI建议（Context-Aware Chips）

#### 触发场景与建议
```
场景1: 用户循环播放同一首歌超过3次
┌──────────────────────────────┐
│  ✨ AI建议                   │
│  [收藏歌曲] [相似推荐] [创作翻唱] │
└──────────────────────────────┘

场景2: 用户连续切歌超过3首
┌──────────────────────────────┐
│  🎵 换个心情？               │
│  [轻松] [激情] [安静] [随机探索] │
└──────────────────────────────┘

场景3: 深夜11点后使用
┌──────────────────────────────┐
│  🌙 夜深了                   │
│  [助眠音乐] [白噪音] [继续摇滚] │
└──────────────────────────────┘
```

#### 就地执行魔法
- 点击建议后，**不跳转页面**
- 内容直接在当前位置"魔法般"更新
- 伴随优雅的过渡动画

### 手势交互系统
- **上滑**: 切换下一首
- **下滑**: 回到上一首  
- **左滑**: 查看创作故事
- **右滑**: 进入相似推荐
- **双击**: 收藏/取消收藏
- **长按**: 分享选项

## 🤖 Layer 3: AI命令面板（主动交互中心）

### 视觉设计
```
默认状态（收起）：
                            ┌─────┐
                            │  ◉  │ ← 呼吸光效
                            │ 小弦 │
                            └─────┘

悬停状态（提示）：
                    ┌──────────────┐
                    │ 单击:快捷回复 │
                    │ 双击:常用功能 │
                    │ 长按:语音输入 │
                    │ 上拉:打开对话 │
                    └──────────────┘
                            ⬇
                         ┌─────┐
                         │  ◉  │
                         └─────┘
```

### 四种交互模式

#### 1. 单击 - 快捷回复轮盘
```
点击后展开扇形选项：
           [😊 开心]
      [😔 忧伤]   [😡 愤怒]
    [😴 疲倦]       [🎉 兴奋]
         [💔 心碎]
              ◉
```
选中后立即切换对应心情歌单

#### 2. 双击 - 常用功能面板
```
┌─────────────────────┐
│   快速创作          │
├─────────────────────┤
│ 🎤 录制哼唱         │
│ 📝 写歌词           │
│ 📸 照片配乐         │
│ 💭 AI帮我写         │
└─────────────────────┘
```

#### 3. 长按 - 语音对话
```
┌─────────────────────┐
│                     │
│      🎤            │
│   正在聆听...      │
│                     │
│ "帮我找一些适合    │
│  工作时听的纯音乐" │
│                     │
│   [松开发送]        │
└─────────────────────┘
```
- 长按开始录音
- 实时语音转文字
- 松开立即执行

#### 4. 向上拖动 - 展开对话框
```
向上拖动后展开为半屏对话界面：
┌─────────────────────────┐
│      与小弦对话         │
├─────────────────────────┤
│ 😊 小弦：有什么需要帮助的吗？│
│                         │
│ [最近流行] [创作灵感]    │
│ [心情不好] [推荐音乐]    │
│                         │
│ ──────────────────────  │
│ 输入你的想法...         │
│ [🎤] [📷] [🎵]         │
├─────────────────────────┤
│ 当前任务：              │
│ ▶️ 正在播放《雨天》      │
│ 📝 草稿：未完成的歌词    │
└─────────────────────────┘
```

### 任务挂起机制
- 对话时，当前播放/编辑状态保持
- 底部显示挂起任务的mini卡片
- 点击立即返回原任务，状态完全恢复

## 🎨 创作场景的AI辅助

### 歌词编辑器的内联建议
```
用户输入：
┌─────────────────────────┐
│ 雨一直下               │
│ 气氛不算融洽           │
│ |                      │ ← 光标位置
└─────────────────────────┘

停顿1.5秒后出现：
┌─────────────────────────┐
│ 雨一直下               │
│ 气氛不算融洽           │
│ |                      │
│ [续写下句] [换个开头] [AI润色] │
└─────────────────────────┘
```

### 智能触发规则
- **停顿超过1.5秒**: 显示续写建议
- **删除超过10个字**: 显示重写建议
- **选中文本**: 显示润色/改写选项
- **输入押韵词**: 自动推荐韵脚

### 一键魔法执行
点击建议后：
1. 按钮变为加载动画
2. 文字渐变出现（打字机效果）
3. 可以继续编辑或撤销

## 🌟 特色创新点

### 1. 分层不打扰
- **顶部丝带**: 被动接收，自动消失
- **内联建议**: 就地解决，不跳转
- **底部面板**: 主动召唤，分级交互

### 2. 上下文感知
- 根据时间、天气、心情自动调整
- 识别用户行为模式，预测需求
- 个性化推荐越用越准

### 3. 极简交互
- 所有操作1-2步完成
- 优先手势，减少点击
- 智能预测，减少选择

### 4. 任务连续性
- 多任务挂起不丢失
- 状态完美保存恢复
- 无缝切换不中断

## 📊 用户体验流程

### 典型使用场景

#### 场景1：通勤听歌
```
7:30 AM 打开APP
↓
AI丝带："早安！准备了你的通勤歌单"
↓
自动播放昨天收藏的类似歌曲
↓
用户觉得太吵，单击AI按钮
↓
快捷选择"安静一点"
↓
播放列表立即切换为轻音乐
```

#### 场景2：深夜创作
```
11:30 PM 进入创作页面
↓
AI丝带："夜深了，来点创作灵感？"
↓
用户写了两句卡住了
↓
内联建议出现："续写下句"
↓
点击后AI生成3个选项
↓
选择其一继续创作
↓
完成后长按AI按钮
↓
语音说："帮我配个R&B编曲"
↓
立即生成并预览
```

#### 场景3：社交互动
```
收到评论通知
↓
AI丝带显示："小红评论了你的新歌"
↓
点击"回复"
↓
底部弹出快速回复
↓
选择表情+预设文案
↓
一键发送
↓
继续听歌不中断
```

## 🎯 设计原则总结

### Do's ✅
- 预测用户需求，提前准备
- 就地解决问题，减少跳转
- 保持任务连续性
- 用动画让AI操作"可见"
- 提供多层次交互选择

### Don'ts ❌
- 不要打断用户操作
- 不要强制对话交互
- 不要遮挡主要内容
- 不要频繁弹出提示
- 不要让AI喧宾夺主

## 💡 核心价值

这个设计让AI小弦成为真正的"隐形助手"：
- **看不见但一直在**: 不占据视觉中心，但随时响应
- **猜得到用户要什么**: 基于上下文智能预测
- **一键解决大部分需求**: 快捷操作覆盖80%场景
- **需要时才深度对话**: 复杂需求时提供完整对话能力

最终实现：**用户听歌创作的体验更流畅，而不是被AI打断**

## TODO
- 将聊天窗口做成单独组件，并且增加：
  - 快捷回复（放在底部）/ 快捷功能按钮
  - 增加长按语音输入的功能，增加上传图片的功能