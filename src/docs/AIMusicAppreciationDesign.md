# AI音乐鉴赏页面核心功能设计

## 设计原则
**信息交互效率优先，情感共鸣为核** 

## 一、核心页面结构（单屏滚动）

### 1. 固定播放器区（上部40%）
- **封面展示**：正方形封面，长按可看动画（类似于实况照片）
- **基础信息**：歌名、创作者、情绪Emoji
- **播放控制**：播放/暂停、进度条、下一首
- **快速操作**：收藏、分享、播放列表

### 2. 歌词互动区（中部40%）
- **当前歌词高亮**：自动滚动定位
- **单击歌词**：显示该句共鸣人数
- **双击歌词**：标记共鸣，并且基于歌词写评论
- **实时数据**：每句歌词右侧显示实时共鸣数

### 3. 底部信息区（下部20%）
- **创作故事**：一句话故事+展开按钮，展开后可查看详细创作故事，并且可评论（故事可以是歌曲屏幕的默认第一条作者评论的样子）
- **热门评论**：显示2条最热评论，点击可查看全部评论
- **二创入口**：仅在允许二创时显示（mock 页面可展示此入口）

## 二、核心功能

### 1. 歌词共鸣系统
**交互方式**：
- 双击任意歌词句 → 顶部出现 AI小弦的指引，提示已经共鸣成功，询问要不要写评论，点击写评论的话，就打开与小弦的聊天的界面，用户在底部聊天输入框中输入内容后发送给小弦，小弦就会生成一个评论条目，告知评论成功，并鼓励下用户；那个评论条目点击后就可以定位到用户评论在评论区的位置；
- 每次共鸣成功都有"+1"动效
- 歌词右侧实时更新共鸣总数，点击可去到评论区此句歌词的专属评论区位置

**价值**：
- 交互成本极低（双击即可）
- 数据价值高（精确到句的情感标记）
- 社交连接强（看到他人共鸣）

### 2. AI小弦智能解读
**触发时机**（仅3个）：
小弦被触发时都是悬浮在顶部，与 APP 其他页面里的顶部小弦智能丝带一样；
用户上滑界面，智能丝带就会自动隐藏起来；
（这里要不要考虑把小弦也固定在顶部，就像其他 APP 页面一样，用户上滑界面，小弦就会自动隐藏起来，用户下滑界面，小弦就会自动出现）
- 首次播放：一句话介绍歌曲亮点
- 播放结束：询问是否查看相似作品
- 高频交互后：建议开始二创
- 点击小弦的音律珠，就会打开与小弦的沟通蒙层，就跟其他页面一样；

## 四、优化后的交互流程

### 主要使用流程（3步内完成）
1. **进入页面** → 自动播放+显示歌词
2. **产生共鸣** → 双击歌词选择情绪
3. **深度互动** → 查看评论/开始二创

### 信息优先级
- **L1必看**：歌曲名、歌词、播放控制
- **L2按需**：创作故事、评论、共鸣数据
- **L3深度**：二创工具、创作者其他作品

## 六、AI小弦的实用化改造

### 核心保留能力
1. **音乐知识科普**：用一句话解释音乐亮点
2. **情感连接**：用户有关于歌曲的情感反馈，小弦就会根据用户的情感给与反馈；
3. **创作引导**：在合适时机推荐二创

## 七、视觉设计简化

### 设计原则
- **信息密度适中**：避免信息过载或过于稀疏
- **交互反馈即时**：200ms内必须有视觉反馈
- **颜色克制使用**：主色调不超过3种

### 具体规范
- 背景：纯色或简单渐变（避免动态背景）
- 字体：只用2个层级（标题/正文）
- 动效：仅保留必要的过渡动画

## 九、核心指标定义

### 成功指标
1. **平均完播率** > 70%
2. **歌词互动率** > 30%（至少标记一句）
3. **二创转化率** > 5%（从听到创）
4. **日均使用时长** > 20分钟

通过这样的设计，用户可以专注于音乐本身和情感表达，而不是被复杂的界面和功能分散注意力。每一个保留的功能都经过效率和价值的双重考验，确保为产品的核心目标服务。